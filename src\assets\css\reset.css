/* reset.css - 完整版 */

/* 基于 <PERSON>'s Reset CSS v2.0 和现代最佳实践的综合重置样式 */

html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed, 
figure, figcaption, footer, header, hgroup, 
menu, nav, output, ruby, section, summary,
time, mark, audio, video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
}

/* HTML5 display-role reset for older browsers */
article, aside, details, figcaption, figure, 
footer, header, hgroup, main, menu, nav, section {
  display: block;
}

/* HTML5 hidden-attribute fix for newer browsers */
*[hidden] {
  display: none;
}

html {
  /* 以设计稿375px为基准，1rem = 100px */
  font-size: calc(100vw / 3.75);
  line-height: 1.15; /* 默认行高 */
  -webkit-text-size-adjust: 100%; /* 防止iPhone在横屏时放大文本 */
  -ms-text-size-adjust: 100%; /* IE兼容 */
}

body {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.14rem; /* 14px */
  line-height: 1.5;
  color: #333;
  background-color: #fff;
}

ol, ul {
  list-style: none;
}

blockquote, q {
  quotes: none;
}

blockquote:before, blockquote:after,
q:before, q:after {
  content: '';
  content: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* 表单元素重置 */
button,
input,
optgroup,
select,
textarea {
  margin: 0;
  font-family: inherit;
  font-size: 100%;
  line-height: 1.15;
}

button,
input {
  overflow: visible;
}

button,
select {
  text-transform: none;
}

button,
[type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button;
}

button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

/* 移除Firefox中的内部outline */
button:-moz-focusring,
[type="button"]:-moz-focusring,
[type="reset"]:-moz-focusring,
[type="submit"]:-moz-focusring {
  outline: 1px dotted ButtonText;
}

/* 修正Firefox中的padding和border */
fieldset {
  padding: 0.35em 0.75em 0.625em;
}

/* IE兼容性修复 */
main {
  display: block;
}

hr {
  box-sizing: content-box;
  height: 0;
  overflow: visible;
}

pre {
  font-family: monospace, monospace;
  font-size: 1em;
}

a {
  background-color: transparent;
  text-decoration: none;
  color: inherit;
}

/* 统一所有元素的盒模型 */
* {
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent; /* 移除移动端点击高亮效果 */
}

/* 修复图片在某些浏览器中的默认边框 */
img {
  max-width: 100%;
  height: auto;
  border-style: none;
  vertical-align: middle; /* 防止图片底部多余空白 */
}

/* 修复SVG默认溢出 */
svg:not(:root) {
  overflow: hidden;
}

/* 为可聚焦元素添加样式 */
:focus {
  outline: 0;
}

/* 修复媒体元素行为 */
audio,
canvas,
iframe,
img,
svg,
video {
  vertical-align: middle;
}

/* 移除audio无控件时的高度 */
audio:not([controls]) {
  display: none;
  height: 0;
}

/* 移动端优化 */
input[type="text"],
input[type="password"],
input[type="number"],
textarea {
  -webkit-appearance: none;
  appearance: none;
}

/* 清除浮动 */
.clearfix::after {
  content: "";
  display: table;
  clear: both;
} 