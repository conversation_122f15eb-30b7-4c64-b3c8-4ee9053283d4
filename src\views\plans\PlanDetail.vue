<template>
  <nav-bar :title="t('plans.planDetail')" :show-bell="false" />
  <div class="content" v-if="loading">
    <!-- 状态卡片骨架屏 -->
    <div class="status-card">
      <div class="status-info">
        <van-skeleton title :row="0" title-width="30%" />
        <div class="status-icon skeleton-icon"></div>
      </div>
    </div>

    <!-- 投放产品骨架屏 -->
    <div class="product-section">
      <div class="product-card">
        <div class="product-image">
          <van-skeleton avatar avatar-size="0.8rem" />
        </div>
        <div class="product-info">
          <van-skeleton title :row="2" />
        </div>
        <div class="product-review">
          <van-skeleton title :row="0" title-width="50%" />
        </div>
      </div>
    </div>

    <!-- 数据概览骨架屏 -->
    <div class="overview-section">
      <div class="overview-header">
        <van-skeleton title :row="0" title-width="40%" />
        <van-skeleton title :row="0" title-width="30%" />
      </div>
      <div class="overview-grid">
        <div class="overview-card" v-for="i in 8" :key="i">
          <div class="overview-main">
            <van-skeleton title :row="0" title-width="50%" />
            <van-skeleton avatar avatar-size="0.22rem" />
          </div>
          <van-skeleton title :row="0" title-width="70%" />
        </div>
      </div>
    </div>

    <!-- 投放规则骨架屏 -->
    <div class="rules-section">
      <van-skeleton title :row="0" title-width="40%" class="skeleton-title" />
      <div class="rules-content">
        <van-skeleton title :row="3" />
      </div>
    </div>

    <!-- 用户定向骨架屏 -->
    <div class="targeting-section">
      <van-skeleton title :row="0" title-width="40%" class="skeleton-title" />
      <div class="targeting-content">
        <van-skeleton title :row="3" />
      </div>
    </div>

    <!-- 创建时间骨架屏 -->
    <div class="creation-section">
      <van-skeleton title :row="0" title-width="40%" class="skeleton-title" />
      <van-skeleton title :row="0" title-width="60%" />
    </div>

    <!-- 投放内容骨架屏 -->
    <div class="ad-content-section">
      <van-skeleton title :row="0" title-width="40%" class="skeleton-title" />
      <div class="ad-content">
        <van-skeleton title :row="0" title-width="100%" class="skeleton-image" />
        <van-skeleton title :row="2" />
        <div class="ad-links">
          <van-skeleton title :row="3" />
        </div>
      </div>
    </div>

    <!-- 产品信息区域骨架屏 -->
    <div class="product-info-section">
      <van-skeleton title :row="0" title-width="40%" class="skeleton-title" />
      <div class="product-info-content">
        <div class="product-info-item" v-for="i in 4" :key="i">
          <van-skeleton title :row="0" title-width="30%" />
          <van-skeleton title :row="0" title-width="40%" />
        </div>
      </div>
    </div>
  </div>
  <div class="content" v-else-if="plan">
    <!-- 状态卡片 -->
    <div class="status-card" :style="{ backgroundColor: statusBgColor }">
      <div class="status-info">
        <div class="status-text">{{ statusText }}</div>
        <div class="status-icon">
          <van-icon :name="statusIcon" />
        </div>
      </div>
    </div>

    <!-- 投放产品 -->
    <div class="product-section">
      <div class="product-card">
        <div class="product-image">
          <van-image width="0.8rem" height="0.8rem" :src="productIcon" fit="cover" radius="4" />
        </div>
        <div class="product-info">
          <div class="product-name">{{ plan.name }}</div>
          <div class="product-desc">{{ plan.category }}</div>
          <div class="product-company">{{ productDeveloper }}</div>
        </div>
        <div class="product-review">
          <div class="review-link" @click="goToProductDetail">{{ t('plans.viewDetails') }}</div>
        </div>
      </div>
    </div>

    <!-- 数据概览 -->
    <div class="overview-section">
      <div class="overview-header">
        <span class="overview-title">{{ t('plans.dataOverview') }}</span>
        <span class="overview-refresh" @click="fetchPlanData">{{ t('plans.refreshInterval') }} <van-icon name="replay"
            class="refresh-icon" /></span>
      </div>
      <div class="overview-grid">
        <!-- 投放金额 -->
        <div class="overview-card">
          <div class="overview-main">
            <span class="overview-value">${{ plan.stats.investedAmount || 0 }}</span>
            <!-- <van-icon name="gold-coin-o" class="overview-icon" /> -->
            <svg t="1749100916692" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
              p-id="8616" width="200" height="200">
              <path
                d="M42.624 512a469.333333 469.333333 0 1 1 469.333333 469.333333 469.333333 469.333333 0 0 1-469.333333-469.333333z m64 0a405.333333 405.333333 0 1 0 405.333333-405.333333 405.76 405.76 0 0 0-405.333333 405.333333z m378.666667 302.933333v-45.397333a233.130667 233.130667 0 0 1-111.232-50.090667 28.885333 28.885333 0 0 1-11.093334-22.656 35.114667 35.114667 0 0 1 8.533334-21.333333 29.866667 29.866667 0 0 1 24.149333-13.098667 33.450667 33.450667 0 0 1 20.010667 6.912 155.605333 155.605333 0 0 0 98.133333 38.741334 82.688 82.688 0 0 0 60.416-20.010667 72.192 72.192 0 0 0 20.266667-54.485333 88.405333 88.405333 0 0 0-28.16-67.584 347.648 347.648 0 0 0-70.485334-43.648 353.450667 353.450667 0 0 1-78.762666-49.92 128.768 128.768 0 0 1-39.68-96.170667 130.304 130.304 0 0 1 34.133333-91.349333 132.650667 132.650667 0 0 1 73.984-40.917334V198.016a28.970667 28.970667 0 0 1 31.104-31.317333 29.269333 29.269333 0 0 1 31.317333 31.317333v45.397333a165.802667 165.802667 0 0 1 89.088 46.165334 33.322667 33.322667 0 0 1 9.429334 21.674666 32.384 32.384 0 0 1-9.429334 21.333334 29.525333 29.525333 0 0 1-22.656 11.349333 34.432 34.432 0 0 1-22.912-10.069333 121.685333 121.685333 0 0 0-33.066666-21.333334 106.069333 106.069333 0 0 0-40.234667-6.656 69.162667 69.162667 0 0 0-51.2 18.986667 66.688 66.688 0 0 0-18.986667 49.322667 70.4 70.4 0 0 0 24.234667 54.997333 317.952 317.952 0 0 0 63.573333 37.248 357.418667 357.418667 0 0 1 85.674667 54.570667 148.906667 148.906667 0 0 1 42.922667 109.738666 136.533333 136.533333 0 0 1-36.266667 96.768 144.341333 144.341333 0 0 1-80 41.898667v45.653333a29.269333 29.269333 0 0 1-31.317333 31.317334 28.970667 28.970667 0 0 1-31.530667-31.445334z"
                p-id="8617"></path>
            </svg>
          </div>
          <div class="overview-label">{{ t('plans.amount') }}</div>
        </div>

        <!-- 投放进度 -->
        <div class="overview-card">
          <div class="overview-main">
            <span class="overview-value">{{ plan.stats.progress || '0%' }}</span>
            <van-icon name="chart-trending-o" class="overview-icon" />
          </div>
          <div class="overview-label">{{ t('plans.placementProgress') }}</div>
        </div>

        <!-- 已消耗 -->
        <div class="overview-card">
          <div class="overview-main">
            <span class="overview-value">${{ plan.stats.consumed || 0 }}</span>
            <!-- <van-icon name="refund-o" class="overview-icon" /> -->
            <svg t="1749101543425" class="icon rotate" viewBox="0 0 1024 1024" version="1.1"
              xmlns="http://www.w3.org/2000/svg" p-id="12974" width="200" height="200">
              <path
                d="M371.5 918.43c-3.38 0-6.81-0.57-10.17-1.78C191.32 855.42 77.1 692.8 77.1 512c0-237.1 192.9-430 430-430 188.27 0 353.02 120.48 409.93 299.79 5.02 15.79-3.73 32.66-19.51 37.67-15.84 4.99-32.66-3.74-37.68-19.52C810.87 245.66 669.12 142 507.1 142c-204.02 0-370 165.98-370 370 0 155.59 98.28 295.51 244.57 348.2 15.59 5.61 23.67 22.79 18.06 38.38-4.41 12.25-15.94 19.85-28.23 19.85zM889.31 660.32c-2.93 0-5.92-0.43-8.87-1.35-15.82-4.88-24.69-21.68-19.79-37.52 10.9-35.27 16.43-72.09 16.43-109.45 0-16.57 13.44-30 30-30s30 13.43 30 30c0 43.36-6.43 86.15-19.12 127.19-3.96 12.87-15.84 21.13-28.65 21.13zM740.74 866.71c-9.32 0-18.5-4.32-24.36-12.46-9.69-13.44-6.64-32.19 6.8-41.88 32.54-23.46 60.9-51.82 84.3-84.3 9.69-13.4 28.42-16.5 41.88-6.8 13.44 9.69 16.48 28.42 6.8 41.88-27.17 37.73-60.12 70.66-97.89 97.89a29.917 29.917 0 0 1-17.53 5.67zM507.1 942c-16.57 0-30-13.44-30-30s13.43-30 30-30c37.33 0 74.15-5.53 109.44-16.45 15.84-4.9 32.64 3.98 37.52 19.79 4.9 15.84-3.96 32.64-19.79 37.52C593.22 935.55 550.45 942 507.1 942z"
                p-id="12975"></path>
              <path
                d="M888.43 420.88c-3.28 0-6.6-0.54-9.86-1.68-15.64-5.45-23.93-22.55-18.48-38.19l28.46-81.72c5.47-15.65 22.62-23.9 38.2-18.47 15.64 5.45 23.93 22.55 18.48 38.19l-28.46 81.72c-4.31 12.38-15.93 20.15-28.34 20.15z"
                p-id="12976"></path>
              <path
                d="M888.41 420.78c-4.22 0-8.48-0.89-12.56-2.77l-78.57-36.28c-15.04-6.94-21.6-24.77-14.67-39.81 6.95-15.03 24.69-21.66 39.82-14.66L901 363.54c15.04 6.94 21.6 24.77 14.67 39.81-5.07 10.96-15.91 17.43-27.26 17.43z"
                p-id="12977"></path>
              <path
                d="M507.1 541.55c-56.47 0-102.41-45.95-102.41-102.41s45.94-102.4 102.41-102.4 102.41 45.94 102.41 102.4c0 16.57-13.44 30-30 30s-30-13.43-30-30c0-23.38-19.02-42.4-42.41-42.4-23.39 0-42.41 19.02-42.41 42.4s19.02 42.4 42.41 42.4c16.57 0 30.01 13.43 30.01 30s-13.44 30.01-30.01 30.01z"
                p-id="12978"></path>
              <path
                d="M504.23 680.63c-54.89 0-99.55-44.67-99.55-99.55 0-16.56 13.43-30 30-30s30 13.44 30 30c0 21.82 17.74 39.55 39.55 39.55s39.55-17.73 39.55-39.55c0-21.8-17.74-39.53-39.55-39.53-16.57 0-30-13.44-30-30.01s13.43-30 30-30c54.88 0 99.55 44.66 99.55 99.54s-44.66 99.55-99.55 99.55zM504.23 396.74c-16.57 0-30-13.43-30-30v-40c0-16.57 13.43-30 30-30s30 13.43 30 30v40c0 16.57-13.42 30-30 30z"
                p-id="12979"></path>
              <path
                d="M507.1 727.27c-16.57 0-30-13.44-30-30v-40c0-16.56 13.43-30 30-30s30.01 13.44 30.01 30v40c0 16.57-13.44 30-30.01 30z"
                p-id="12980"></path>
            </svg>
          </div>
          <div class="overview-label">{{ t('plans.consumed') }}</div>
        </div>

        <!-- 待消耗 -->
        <div class="overview-card">
          <div class="overview-main">
            <span class="overview-value">${{ plan.stats.pendingConsumption || 0 }}</span>
            <!-- <van-icon name="after-sale" class="overview-icon" /> -->
            <svg t="1749101543425" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
              p-id="12974" width="200" height="200">
              <path
                d="M371.5 918.43c-3.38 0-6.81-0.57-10.17-1.78C191.32 855.42 77.1 692.8 77.1 512c0-237.1 192.9-430 430-430 188.27 0 353.02 120.48 409.93 299.79 5.02 15.79-3.73 32.66-19.51 37.67-15.84 4.99-32.66-3.74-37.68-19.52C810.87 245.66 669.12 142 507.1 142c-204.02 0-370 165.98-370 370 0 155.59 98.28 295.51 244.57 348.2 15.59 5.61 23.67 22.79 18.06 38.38-4.41 12.25-15.94 19.85-28.23 19.85zM889.31 660.32c-2.93 0-5.92-0.43-8.87-1.35-15.82-4.88-24.69-21.68-19.79-37.52 10.9-35.27 16.43-72.09 16.43-109.45 0-16.57 13.44-30 30-30s30 13.43 30 30c0 43.36-6.43 86.15-19.12 127.19-3.96 12.87-15.84 21.13-28.65 21.13zM740.74 866.71c-9.32 0-18.5-4.32-24.36-12.46-9.69-13.44-6.64-32.19 6.8-41.88 32.54-23.46 60.9-51.82 84.3-84.3 9.69-13.4 28.42-16.5 41.88-6.8 13.44 9.69 16.48 28.42 6.8 41.88-27.17 37.73-60.12 70.66-97.89 97.89a29.917 29.917 0 0 1-17.53 5.67zM507.1 942c-16.57 0-30-13.44-30-30s13.43-30 30-30c37.33 0 74.15-5.53 109.44-16.45 15.84-4.9 32.64 3.98 37.52 19.79 4.9 15.84-3.96 32.64-19.79 37.52C593.22 935.55 550.45 942 507.1 942z"
                p-id="12975"></path>
              <path
                d="M888.43 420.88c-3.28 0-6.6-0.54-9.86-1.68-15.64-5.45-23.93-22.55-18.48-38.19l28.46-81.72c5.47-15.65 22.62-23.9 38.2-18.47 15.64 5.45 23.93 22.55 18.48 38.19l-28.46 81.72c-4.31 12.38-15.93 20.15-28.34 20.15z"
                p-id="12976"></path>
              <path
                d="M888.41 420.78c-4.22 0-8.48-0.89-12.56-2.77l-78.57-36.28c-15.04-6.94-21.6-24.77-14.67-39.81 6.95-15.03 24.69-21.66 39.82-14.66L901 363.54c15.04 6.94 21.6 24.77 14.67 39.81-5.07 10.96-15.91 17.43-27.26 17.43z"
                p-id="12977"></path>
              <path
                d="M507.1 541.55c-56.47 0-102.41-45.95-102.41-102.41s45.94-102.4 102.41-102.4 102.41 45.94 102.41 102.4c0 16.57-13.44 30-30 30s-30-13.43-30-30c0-23.38-19.02-42.4-42.41-42.4-23.39 0-42.41 19.02-42.41 42.4s19.02 42.4 42.41 42.4c16.57 0 30.01 13.43 30.01 30s-13.44 30.01-30.01 30.01z"
                p-id="12978"></path>
              <path
                d="M504.23 680.63c-54.89 0-99.55-44.67-99.55-99.55 0-16.56 13.43-30 30-30s30 13.44 30 30c0 21.82 17.74 39.55 39.55 39.55s39.55-17.73 39.55-39.55c0-21.8-17.74-39.53-39.55-39.53-16.57 0-30-13.44-30-30.01s13.43-30 30-30c54.88 0 99.55 44.66 99.55 99.54s-44.66 99.55-99.55 99.55zM504.23 396.74c-16.57 0-30-13.43-30-30v-40c0-16.57 13.43-30 30-30s30 13.43 30 30v40c0 16.57-13.42 30-30 30z"
                p-id="12979"></path>
              <path
                d="M507.1 727.27c-16.57 0-30-13.44-30-30v-40c0-16.56 13.43-30 30-30s30.01 13.44 30.01 30v40c0 16.57-13.44 30-30.01 30z"
                p-id="12980"></path>
            </svg>
          </div>
          <div class="overview-label">{{ t('plans.remaining') }}</div>
        </div>

        <!-- 展示数 -->
        <div class="overview-card">
          <div class="overview-main">
            <span class="overview-value">{{ plan.stats.impressions || 0 }}</span>
            <van-icon name="eye-o" class="overview-icon" />
          </div>
          <div class="overview-label">{{ t('plans.impressions') }}</div>
        </div>

        <!-- 点击数 -->
        <div class="overview-card">
          <div class="overview-main">
            <span class="overview-value">{{ plan.stats.clicks || 0 }}</span>
            <van-icon name="star-o" class="overview-icon" />
          </div>
          <div class="overview-label">{{ t('plans.clicks') }}</div>
        </div>

        <!-- 广告收入 -->
        <div class="overview-card">
          <div class="overview-main">
            <span class="overview-value">${{ plan.stats.income || 0 }}</span>
            <!-- <van-icon name="balance-o" class="overview-icon" /> -->
            <svg t="1749101403911" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
              p-id="8784" width="200" height="200">
              <path
                d="M526.295 480.346h4.857c36.436 7.288 63.156 19.433 80.158 38.862 17.003 19.433 26.718 43.724 26.718 72.871 0 31.577-9.715 58.298-29.147 80.157-19.43 21.86-48.581 34.006-82.586 34.006v48.58h-26.72v-46.15c-36.432-4.856-63.152-14.575-82.585-34.005-19.433-19.433-29.147-46.153-31.578-80.157l46.153-7.289c4.857 26.722 12.145 46.151 21.86 58.297 12.144 14.574 26.72 21.861 43.722 24.289V519.208c-36.435-7.284-60.725-19.429-80.158-38.862-17.002-17.003-26.717-41.293-26.717-68.014 0-29.147 9.714-51.007 26.717-72.87 17.002-19.433 43.724-31.578 80.158-34.005v-24.29h26.718v21.86c29.15 2.43 53.44 12.145 70.443 29.147 17.002 17.006 26.717 38.866 31.577 65.586l-48.58 7.285c-2.43-17.003-9.718-31.575-17.006-41.293-9.715-9.714-21.859-17.002-36.435-19.433v136.027h2.431z m-26.72-7.288V344.319c-19.429 2.431-34.005 9.719-43.72 21.864-9.718 12.145-17.002 26.717-17.002 41.292 0 17.003 4.857 31.578 14.573 41.293 12.143 12.145 26.72 19.433 46.149 24.29z m26.72 194.321c19.433-2.429 34.005-9.717 46.15-24.29 12.146-12.145 17.006-29.146 17.006-48.579 0-17.004-4.86-31.578-14.575-41.294-9.716-9.717-24.291-19.433-48.581-26.72v140.883z m0 0"
                p-id="8785"></path>
              <path
                d="M900.362 244.732v128.739c-46.149 31.575-72.87 82.585-72.87 138.453 0 55.864 26.721 106.875 72.87 138.453v128.736H123.081v-126.31c46.15-31.577 75.298-82.585 75.298-140.88 0-58.298-29.148-109.306-75.298-140.884V244.732h777.281m48.58-48.581H74.5v204.036c43.723 17.003 75.301 60.726 75.301 111.736 0 51.008-31.578 94.73-75.301 111.732v204.036h874.442V623.656c-43.723-19.433-72.87-60.725-72.87-111.732 0-48.581 29.147-92.304 72.87-111.736V196.151z m0 0"
                p-id="8786"></path>
            </svg>
          </div>
          <div class="overview-label">{{ t('plans.adRevenue') }}</div>
        </div>

        <!-- 利润 -->
        <div class="overview-card">
          <div class="overview-main">
            <span class="overview-value">${{ plan.stats.profit || 0 }}</span>
            <!-- <van-icon name="balance-pay" class="overview-icon" /> -->
            <svg t="1749101454712" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
              p-id="10934" width="200" height="200">
              <path
                d="M153.92 554.56H153.6c-22.72 8.64-37.76 30.08-38.4 54.4l-1.92 95.36v90.88c0 13.76 6.08 26.88 16.96 35.84l16.64 13.76c8 6.72 17.92 10.24 28.48 9.92l63.04-1.28 192.32 87.04a65.504 65.504 0 0 0 64.32-6.08l389.12-274.56c24.32-17.28 34.56-49.6 21.76-76.8-12.8-26.88-41.6-40-70.08-32.32l-371.2 98.88-114.56-98.24v-0.32l-184.32 1.92c-3.52 0-7.04 0.32-10.56 0.96m704 67.2l-391.04 275.84a19.84 19.84 0 0 1-18.88 1.92l-201.92-91.52-79.04 1.92a12.48 12.48 0 0 1-5.76-2.24 8.096 8.096 0 0 1-2.56-5.76L159.04 704v-100.48c0.32-0.96 0.96-1.6 1.28-1.92 0.96-0.96 2.88-2.56 5.76-2.56l167.04-1.6 158.08 135.36c11.2 9.6 14.72 25.6 7.68 38.4a30.4 30.4 0 0 1-46.72 8.64l-109.12-93.44-29.76 34.88 109.12 93.44c14.08 11.84 31.36 18.24 49.6 18.24 1.92 0 4.16 0 6.08-0.32a74.88 74.88 0 0 0 52.16-26.56c14.4-16.64 20.8-38.4 17.6-60.48-2.56-19.2-13.44-36.48-28.16-48.96l-12.8-10.88 339.84-90.56c10.56-2.88 15.36 4.48 16.96 8 1.6 3.2 4.48 11.52-4.48 17.92z"
                fill="#2c2c2c" p-id="10935"></path>
              <path
                d="M487.36 557.76c-132.48 0-240-107.84-240-240s107.84-240 240-240 240 107.84 240 240-107.52 240-240 240z m0-434.24c-107.2 0-194.24 87.04-194.24 194.24S380.48 512 487.36 512s194.24-87.04 194.24-194.24-87.04-194.24-194.24-194.24z"
                fill="#2c2c2c" p-id="10936"></path>
              <path
                d="M565.76 273.28v-10.56c0-35.52-22.72-67.2-55.36-77.44v-23.04h-45.76v23.04c-32 9.92-55.36 39.68-55.36 74.56 0 14.72 6.08 29.12 16.96 39.04l90.24 63.04c1.92 2.56 3.2 5.44 3.2 8.64v6.72c0 20.16-18.24 35.84-39.04 31.68-15.04-3.2-25.6-17.28-25.6-32.64v-17.6h-45.76v18.56c0 35.2 23.36 64.96 55.36 74.56v21.12h45.76v-21.12c32.32-10.24 55.36-41.92 55.36-77.44v-3.84c0-10.88-2.88-21.44-8.32-30.72-2.56-4.16-5.76-7.68-9.92-10.56l-89.92-64c-1.6-1.28-2.24-3.2-2.24-5.44 0-20.16 18.24-35.84 39.04-31.68 15.04 3.2 25.6 17.28 25.6 32.64v12.48h45.76z"
                fill="#2c2c2c" p-id="10937"></path>
            </svg>
          </div>
          <div class="overview-label">{{ t('plans.profit') }}</div>
        </div>
      </div>
    </div>

    <!-- 投放规则 -->
    <div class="rules-section" v-if="plan.promotion">
      <div class="rules-header">{{ t('plans.placementRules') }}</div>
      <div class="rules-content">
        <div class="rule-item" v-for="(value, key) in plan.promotion" :key="key">
          <div class="rule-label">{{ key }}</div>
          <div class="rule-value">{{ value }}</div>
        </div>
      </div>
    </div>

    <!-- 用户定向 -->
    <div class="targeting-section" v-if="plan.audience">
      <div class="targeting-header">{{ t('plans.userTargeting') }}</div>
      <div class="targeting-content">
        <div class="targeting-item" v-for="(value, key) in plan.audience" :key="key">
          <div class="targeting-label">{{ key }}</div>
          <div class="targeting-value">{{ value }}</div>
        </div>
      </div>
    </div>

    <!-- 创建时间 -->
    <div class="creation-section">
      <div class="creation-header">{{ t('plans.createTime') }}</div>
      <div class="creation-content">{{ plan.createTime }}</div>
    </div>

    <!-- 投放内容 -->
    <div class="ad-content-section">
      <div class="ad-content-header">{{ t('plans.adContent') }}</div>
      <div class="ad-content">
        <img :src="plan.appBannerUrl || plan.appIconUrl" alt="Ad Content" class="ad-image" />
        <div class="ad-text">
          {{ plan.description }}
        </div>
        <div class="ad-links">
          <div class="ad-link-item">
            <div class="ad-link-label">{{ t('plans.promotionalLink') }}</div>
            <div class="ad-link-value">{{ plan.promotionalLink || 'https://example.com' }}</div>
          </div>
          <div class="ad-link-item">
            <div class="ad-link-label">{{ t('plans.adDisplayFormat') }}</div>
            <div class="ad-link-value">Image</div>
          </div>
          <div class="ad-link-item">
            <div class="ad-link-label">{{ t('plans.conversionMethod') }}</div>
            <div class="ad-link-value">{{ t('plans.landingPage') }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 产品信息区域 -->
    <div class="product-info-section" v-if="plan.productInfo">
      <div class="product-info-header">产品信息</div>
      <div class="product-info-content">
        <div class="product-info-item">
          <div class="product-info-label">产品名称</div>
          <div class="product-info-value">{{ plan.productInfo.name }}</div>
        </div>
        <div class="product-info-item">
          <div class="product-info-label">分类</div>
          <div class="product-info-value">{{ plan.productInfo.category }}</div>
        </div>
        <div class="product-info-item">
          <div class="product-info-label">开发者</div>
          <div class="product-info-value">{{ plan.productInfo.developer }}</div>
        </div>
        <div class="product-info-item">
          <div class="product-info-label">评分</div>
          <div class="product-info-value">{{ plan.productInfo.rating }}</div>
        </div>
      </div>
    </div>
  </div>
  <div class="content" v-else>
    <div class="error-message">
      <van-empty :description="t('plans.planNotFound')" />
      <van-button type="primary" block @click="$router.push('/plans')">{{ t('plans.backToPlans') }}</van-button>
    </div>
  </div>
</template>

<script setup>
import { useRoute, useRouter } from 'vue-router'
import {
  Cell as VanCell,
  CellGroup as VanCellGroup,
  Icon as VanIcon,
  Image as VanImage,
  showToast,
  Loading as VanLoading,
  Empty as VanEmpty,
  Button as VanButton,
  Skeleton as VanSkeleton
} from 'vant'
import NavBar from '@/components/NavBar.vue'
import { computed, ref, onMounted, onUnmounted, watch } from 'vue'
import { useTitle } from '@/utils/useTitle'
import { useI18n } from 'vue-i18n'
import { planApi } from '@/api'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const id = computed(() => route.params.id)
// 自动刷新定时器引用
const autoRefreshTimer = ref(null)
// 计划数据
const plan = ref(null)
const loading = ref(true)

// 获取计划数据
const fetchPlanData = async () => {
  loading.value = true
  try {
    const res = await planApi.getPlanDetail(id.value)
    if (res && res.data) {
      plan.value = res.data
    } else {
      showToast(t('plans.planNotFound'))
      router.push('/plans')
    }
  } catch (error) {
    console.error('获取计划数据失败:', error)
    showToast(t('fetchFailed'))
    router.push('/plans')
  } finally {
    loading.value = false
  }
}

// 计算属性：计划状态文本
const statusText = computed(() => {
  if (!plan.value) return ''
  return plan.value.status || t('unknownStatus')
})

// 计算属性：计划状态图标
const statusIcon = computed(() => {
  if (!plan.value) return 'question-o'

  const statusIconMap = {
    '待投放': 'clock-o',
    '匹配中': 'search',
    '投放中': 'play-circle-o',
    '投放失败': 'close-circle-o',
    '投放完成': 'success'
  }

  return statusIconMap[plan.value.status] || 'question-o'
})

// 计算属性：计划状态背景色
const statusBgColor = computed(() => {
  if (!plan.value) return '#1877F2'

  const statusBgColorMap = {
    '待投放': '#1877F2',
    '匹配中': '#1877F2',
    '投放中': '#1877F2',
    '投放失败': '#ff4d4f',
    '投放完成': '#52c41a'
  }

  return statusBgColorMap[plan.value.status] || '#1877F2'
})

// 计算属性：产品图标
const productIcon = computed(() => {
  return plan.value?.productInfo?.icon || plan.value?.appIconUrl || ''
})

// 计算属性：产品开发者
const productDeveloper = computed(() => {
  return plan.value?.productInfo?.developer || t('plans.appDeveloper')
})

// 跳转到产品详情页面
const goToProductDetail = () => {
  if (plan.value) {
    // 使用 productId 字段，如果不存在则使用 plan_code
    const productId = plan.value.productId || plan.value.plan_code
    router.push(`/plans/product/${productId}`)
  }
}

onMounted(() => {
  fetchPlanData()
  startAutoRefresh()
})

// 组件卸载时清理定时器
onUnmounted(() => {
  stopAutoRefresh()
})
// 监视 planId 变化，重新获取详情
watch(
  () => id.value,
  (newId, oldId) => {
    if (newId && newId !== oldId) {
      fetchPlanData()
    }
  },
)

// 开始自动刷新
const startAutoRefresh = () => {
  // 先清除之前的定时器（如果存在）
  stopAutoRefresh()

  // 设置定时刷新
  autoRefreshTimer.value = setInterval(() => {
    fetchPlanData(true)
  }, 30000) // 转换为毫秒
}

// 停止自动刷新
const stopAutoRefresh = () => {
  if (autoRefreshTimer.value) {
    clearInterval(autoRefreshTimer.value)
    autoRefreshTimer.value = null
  }
}

// 设置页面标题
useTitle(() => `${t('plans.planDetail')}${id.value}`)
</script>

<style scoped>
.content {
  padding: 0.16rem;
  background-color: #f5f5f5;
}

/* 状态卡片 */
.status-card {
  background-color: #1877F2;
  border-radius: 0.08rem;
  padding: 0.16rem;
  margin-bottom: 0.16rem;
}

.status-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-text {
  font-size: 0.2rem;
  font-weight: bold;
  color: #fff;
}

.status-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 0.4rem;
  height: 0.4rem;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}

.status-icon .van-icon {
  font-size: 0.24rem;
  color: #fff;
}

/* 投放产品 */
.product-section {
  margin-bottom: 0.16rem;
}

.product-card {
  background-color: #fff;
  border-radius: 0.08rem;
  padding: 0.16rem;
  display: flex;
  align-items: center;
}

.product-image {
  margin-right: 0.12rem;
}

.product-info {
  flex: 1;
}

.product-name {
  font-size: 0.16rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.04rem;
}

.product-desc {
  font-size: 0.12rem;
  color: #666;
  margin-bottom: 0.04rem;
}

.product-company {
  font-size: 0.12rem;
  color: #999;
}

.product-review {
  color: #1877F2;
  font-size: 0.14rem;
}

.review-link {
  cursor: pointer;
}

.review-link:hover {
  text-decoration: underline;
}

/* 数据概览 */
.overview-section {
  background: #fff;
  padding: 0.18rem 0.12rem 0.12rem 0.12rem;
  border-radius: 0.08rem;
  margin-bottom: 0.16rem;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.12rem;
}

.overview-title {
  font-size: 0.16rem;
  font-weight: bold;
}

.overview-refresh {
  font-size: 0.13rem;
  color: #888;
  display: flex;
  align-items: center;
  gap: 0.02rem;
}

.overview-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.14rem;
}

.overview-card {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 0.12rem;
  padding: 0.1rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 0.7rem;
}

.overview-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.08rem;
}
.overview-main svg {
  width: 0.2rem;
  height: 0.2rem;
}
.overview-main .rotate {
  transform: rotate(180deg);
}
.overview-value {
  font-size: 0.16rem;
  font-weight: bold;
  color: #111;
}

.overview-icon {
  font-size: 0.22rem;
  color: #222;
}

.overview-label {
  font-size: 0.12rem;
  color: #222;
}

.refresh-icon {
  color: #1989fa;
}

/* 投放规则、用户定向、创建时间、广告内容区域 */
.rules-section,
.targeting-section,
.creation-section,
.ad-content-section,
.product-info-section {
  background: #fff;
  padding: 0.16rem;
  border-radius: 0.08rem;
  margin-bottom: 0.16rem;
}

.rules-header,
.targeting-header,
.creation-header,
.ad-content-header,
.product-info-header {
  font-size: 0.16rem;
  font-weight: bold;
  margin-bottom: 0.12rem;
}

.rules-content,
.targeting-content,
.product-info-content {
  display: flex;
  flex-direction: column;
  gap: 0.1rem;
}

.rule-item,
.targeting-item,
.ad-link-item,
.product-info-item {
  display: flex;
  justify-content: space-between;
  padding: 0.08rem 0;
  border-bottom: 1px solid #f5f5f5;
}

.rule-item:last-child,
.targeting-item:last-child,
.ad-link-item:last-child,
.product-info-item:last-child {
  border-bottom: none;
}

.rule-label,
.targeting-label,
.ad-link-label,
.product-info-label {
  color: #666;
  font-size: 0.14rem;
}

.rule-value,
.targeting-value,
.ad-link-value,
.product-info-value {
  color: #333;
  font-size: 0.14rem;
  font-weight: 500;
  word-break: break-all;
  padding-left: 0.1rem;
}

.creation-content {
  color: #333;
  font-size: 0.14rem;
}

/* 投放内容 */
.ad-content {
  display: flex;
  flex-direction: column;
  gap: 0.12rem;
}

.ad-image {
  width: 100%;
  border-radius: 0.08rem;
  margin-bottom: 0.12rem;
}

.ad-text {
  font-size: 0.14rem;
  color: #333;
  line-height: 1.5;
  margin-bottom: 0.12rem;
}

.ad-links {
  display: flex;
  flex-direction: column;
  gap: 0.1rem;
}

/* 错误消息 */
.error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

/* 骨架屏样式 */
.skeleton-icon {
  background-color: #f2f3f5;
}

.skeleton-title {
  margin-bottom: 0.12rem;
}

.skeleton-image {
  height: 2rem;
  margin-bottom: 0.12rem;
}

:deep(.van-skeleton__title) {
  height: 0.16rem;
}

:deep(.van-skeleton__row) {
  height: 0.14rem;
  margin-top: 0.08rem;
}

:deep(.van-skeleton__avatar) {
  background-color: #f2f3f5;
}

.overview-card :deep(.van-skeleton) {
  height: 100%;
}

.overview-card :deep(.van-skeleton__avatar) {
  margin-right: 0;
}

.product-info-item {
  display: flex;
  justify-content: space-between;
  padding: 0.08rem 0;
  border-bottom: 1px solid #f5f5f5;
}

.product-info-item:last-child {
  border-bottom: none;
}

.product-info-item :deep(.van-skeleton) {
  width: auto;
}
</style>