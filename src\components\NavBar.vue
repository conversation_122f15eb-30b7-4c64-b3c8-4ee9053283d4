<template>
  <van-nav-bar
    :title="title"
    :left-text="leftText"
    :right-text="rightText"
    :left-arrow="showLeftArrow"
    @click-left="onClickLeft"
    @click-right="onClickRight"
    class="custom-nav-bar"
  >
    <template #right>
      <van-icon v-if="showBell" name="bell" class="custom-icon" />
      <van-icon v-else-if="rightIcon" :name="rightIcon" class="custom-icon" />
    </template>
  </van-nav-bar>
</template>

<script setup>
import { useNavigation } from '@/utils/navigation'

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  leftText: {
    type: String,
    default: ''
  },
  rightText: {
    type: String,
    default: ''
  },
  rightIcon: {
    type: String,
    default: ''
  },
  showLeftArrow: {
    type: Boolean,
    default: true
  },
  showBell: {
    type: Boolean,
    default: true
  },
  useDefaultNavigation: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['click-left', 'click-right'])
const navigation = useNavigation()

const onClickLeft = () => {
  if (props.useDefaultNavigation) {
    navigation.goBack()
  } else {
    emit('click-left')
  }
}

const onClickRight = () => {
  if (props.showBell) {
    navigation.goToNotificationCenter()
  } else {
    emit('click-right')
  }
}
</script>

<style scoped>
.custom-icon {
  font-size: 0.22rem;
  color: black;
}

.custom-nav-bar :deep(.van-icon-arrow-left) {
  color: black;
  font-size: 0.22rem;
  -webkit-text-stroke: 0.002rem black;
  /* transform: scale(1.2); */
}

.van-hairline--bottom::after {
  border-bottom-width: 0;
}
</style> 