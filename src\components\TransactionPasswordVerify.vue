<template>
  <van-action-sheet
    v-model:show="showVerify"
    :title="finalTitle"
    :close-on-click-overlay="closeOnClickOverlay"
    @closed="onClosed"
    :closeable="false"
  >
    <div class="password-container">
      <van-password-input
        :value="password"
        :focused="true"
        :length="passwordLength"
        @focus="showKeyboard = true"
        :gutter="20"
      />
      <div class="tip-text">{{ finalTipText }}</div>
      <div v-if="errorMessage" class="error-text">{{ errorMessage }}</div>
      <van-number-keyboard
        v-model:show="showKeyboard"
        v-model="password"
        :maxlength="passwordLength"
        @blur="onKeyboardBlur"
        :hide-on-click-outside="false"
        :close-button="false"
      />
    </div>
  </van-action-sheet>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { showToast, showLoadingToast, closeToast, PasswordInput as VanPasswordInput, NumberKeyboard as VanNumberKeyboard } from 'vant'
import { useI18n } from 'vue-i18n'
import { verifyPaymentPassword } from '@/api/wallet'

const { t } = useI18n()

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: ''
  },
  tipText: {
    type: String,
    default: ''
  },
  passwordLength: {
    type: Number,
    default: 6
  },
  closeOnClickOverlay: {
    type: Boolean,
    default: true
  },
  skipVerification: {
    type: Boolean,
    default: false
  }
})

// 使用计算属性处理国际化默认值
const finalTitle = computed(() => 
  props.title || t('password.passwordVerifyTitle')
)
const finalTipText = computed(() => 
  props.tipText || t('password.passwordVerifyTips')
)

const emit = defineEmits(['update:show', 'confirm', 'cancel', 'verification-error'])

const showVerify = ref(props.show)
const showKeyboard = ref(true)
const password = ref('')
const verifying = ref(false)
const errorMessage = ref('')
const verificationSuccess = ref(false)

// 监听外部show属性变化
watch(() => props.show, (newVal) => {
  showVerify.value = newVal
  if (newVal) {
    password.value = ''
    errorMessage.value = ''
    showKeyboard.value = true
    verificationSuccess.value = false
  }
})

// 监听内部showVerify变化，同步到外部
watch(showVerify, (newVal) => {
  emit('update:show', newVal)
  if (!newVal && !verificationSuccess.value) {
    emit('cancel')
  }
})

// 验证密码
const verifyPassword = async (pwd) => {
  if (verifying.value) return
  
  verifying.value = true
  errorMessage.value = ''
  
  // 显示加载提示
  showLoadingToast({
    message: t('password.verifying'),
    forbidClick: true,
    duration: 0
  })
  
  try {
    if (props.skipVerification) {
      // 如果不需要验证，直接返回成功
      verificationSuccess.value = true // 设置成功标记
      closeToast() // 关闭loading
      emit('confirm', pwd)
      // 不再自动关闭弹窗，由调用方决定是否关闭
      return
    }
    
    const res = await verifyPaymentPassword({ payPassword: pwd })
    
    // 关闭loading
    closeToast()
    
    if (res.data && res.data.verified) {
      // 验证成功
      verificationSuccess.value = true // 设置成功标记
      emit('confirm', pwd)
      // 不再自动关闭弹窗，由调用方决定是否关闭
    } else {
      // 验证失败
      errorMessage.value = t('password.verificationFailed')
      password.value = ''
      emit('verification-error', { message: errorMessage.value })
    }
  } catch (error) {
    // 关闭loading
    closeToast()
    
    // 处理错误
    console.error('支付密码验证失败:', error)
    errorMessage.value = error.message || t('password.verificationError')
    password.value = ''
    emit('verification-error', { error, message: errorMessage.value })
  } finally {
    verifying.value = false
  }
}

// 监听密码输入完成
watch(password, (newVal) => {
  if (newVal.length === props.passwordLength) {
    verifyPassword(newVal)
  }
})

const onKeyboardBlur = () => {
  showKeyboard.value = true
}

const onClosed = () => {
  password.value = ''
  errorMessage.value = ''
}

// 暴露关闭弹窗方法，让调用方可以主动关闭
const closeVerify = (success = false) => {
  verificationSuccess.value = success
  showVerify.value = false
}

// 暴露方法给外部使用
defineExpose({
  closeVerify
})
</script>

<style scoped>
.password-container {
  padding: 0.3rem 0.16rem 3rem;
  background-color: #f8f8f8;
}

.tip-text {
  margin-top: 0.1rem;
  color: #969799;
  font-size: 0.14rem;
  text-align: center;
}

.error-text {
  margin-top: 0.1rem;
  color: #ee0a24;
  font-size: 0.14rem;
  text-align: center;
}
</style> 