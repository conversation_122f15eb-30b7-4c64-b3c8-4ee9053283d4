# Facebook广告投放平台 - 移动端

## 项目介绍

本项目为广告联盟平台的手机用户端，提供广告账户管理、用户中心、广告投放等功能。

## 技术栈

- Vue 3 - 前端框架
- Vant UI - 移动端组件库
- Pinia - 状态管理
- Vue Router - 路由管理
- Axios - HTTP 请求
- Vue I18n - 国际化
- Vite - 构建工具

## 项目结构

```
facebook-ad/
├── src/
│   ├── api/                # API接口
│   │   ├── adCenter.js     # 广告中心相关接口
│   │   └── index.js        # API入口
│   ├── assets/             # 资源文件
│   │   ├── css/            # CSS 样式文件
│   │   │   └── reset.css   # 样式重置
│   │   └── image/          # 图片资源
│   │       ├── adcenter/   # 广告中心相关图片
│   │       └── account/    # 账户相关图片
│   ├── components/         # 公共组件
│   │   └── NavBar.vue      # 导航栏组件
│   ├── i18n/               # 国际化配置
│   │   ├── index.js        # i18n 初始化
│   │   └── locales/        # 语言文件
│   │       ├── zh/         # 中文翻译
│   │       │   ├── index.js   # 中文翻译入口
│   │       │   ├── common.js  # 公共翻译
│   │       │   ├── account.js # 账户翻译
│   │       │   ├── adcenter.js # 广告中心翻译
│   │       │   ├── bankcard.js # 银行卡翻译
│   │       │   ├── password.js # 密码翻译
│   │       │   ├── wallet.js   # 钱包翻译
│   │       │   └── plans.js    # 计划中心翻译
│   │       └── en/         # 英文翻译
│   ├── router/             # 路由配置
│   │   └── index.js        # 路由定义
│   ├── stores/             # Pinia 状态管理
│   │   ├── index.js        # 状态管理入口
│   │   └── user.js         # 用户状态管理
│   ├── utils/              # 工具函数
│   │   ├── request.js      # Axios 请求封装
│   │   ├── navigation.js   # 导航工具
│   │   ├── useTitle.js     # 页面标题工具
│   │   └── useUserInfo.js  # 用户信息工具
│   ├── views/              # 页面组件
│   │   ├── Home.vue        # 首页
│   │   ├── Account.vue     # 账户页面
│   │   ├── AdCenter.vue    # 广告投放中心
│   │   ├── Plans.vue       # 计划中心
│   │   ├── adcenter/       # 广告中心子页面
│   │   │   ├── Commission.vue  # 佣金中心
│   │   │   ├── Coupon.vue      # 优惠券
│   │   │   ├── Feedback.vue    # 用户反馈
│   │   │   ├── FAQ.vue         # 常见问题
│   │   │   ├── Message.vue     # 消息中心
│   │   │   ├── Newcomer.vue    # 新人领取优惠券
│   │   │   ├── Operation.vue   # 代运营
│   │   │   ├── Service.vue     # 在线客服
│   │   │   ├── VIP.vue         # VIP权益
│   │   │   ├── Wallet.vue      # 我的钱包
│   │   │   └── wallet/         # 钱包相关子页面
│   │   │       ├── BankCard.vue # 绑定银行卡
│   │   │       ├── Crypto.vue   # 绑定虚拟货币
│   │   │       └── Password.vue # 交易密码设置
│   │   └── plans/          # 计划中心子页面
│   │       ├── PlanDetail.vue   # 计划详情
│   │       └── plan/           # 计划相关子页面
│   │           └── ProductDetail.vue # 产品详情
│   ├── App.vue             # 应用入口组件
│   ├── main.js             # 应用入口文件
│   └── style.css           # 全局样式
├── public/                 # 静态资源目录
├── .env                    # 环境变量
├── .env.local              # 本地环境变量（不提交到版本控制）
├── .gitignore              # Git忽略配置
├── index.html              # HTML 模板
├── package.json            # 依赖配置
├── README.md               # 项目说明文档
└── vite.config.js          # Vite 配置
```

## 页面结构与路由

```
/ (Home)                    # 首页
/account (Account)          # 账户页面
/adcenter (AdCenter)        # 广告投放中心
├── /adcenter/commission    # 佣金中心
├── /adcenter/coupon        # 优惠券
├── /adcenter/feedback      # 用户反馈
├── /adcenter/faq           # 常见问题
├── /adcenter/message       # 消息中心
├── /adcenter/newcomer      # 新人领取优惠券
├── /adcenter/operation     # 代运营
├── /adcenter/service       # 在线客服
├── /adcenter/vip           # VIP权益
└── /adcenter/wallet        # 我的钱包
    ├── /adcenter/wallet/bank     # 绑定银行卡
    ├── /adcenter/wallet/crypto   # 绑定虚拟货币
    └── /adcenter/wallet/password # 交易密码设置
/plans (Plans)              # 计划中心
├── /plans/plan/:id         # 计划详情（带参数）
└── /plans/product/:productId # 产品详情（带参数）
```

## 项目开发注意事项

### 基础配置
- 请求所有接口时需要携带token
- 项目根目录使用@符号表示src目录
- 使用.env.local文件配置环境变量
- 环境变量包括：
  - VITE_APP_BASE_API - API基础路径
  - VITE_APP_TITLE - 应用标题
  - VITE_APP_TOKEN_KEY - 本地存储token的键名
  - VITE_APP_USER_INFO_KEY - 本地存储用户信息的键名

### 导航栏组件
- 项目使用统一的NavBar组件，支持以下配置：
  - `:show-left-arrow="false"` - 控制是否显示返回按钮，默认显示
  - `:show-bell="false"` - 控制是否显示铃铛按钮，默认显示
  - `:use-default-navigation="true"` - 控制是否使用默认导航行为，默认启用
  - `:title="t('message.key')"` - 设置导航栏标题，支持国际化
  - `:right-icon="iconName"` - 设置右侧图标（当不显示铃铛时）
  - `:left-text="textValue"` - 设置左侧文本
  - `:right-text="textValue"` - 设置右侧文本
- 特殊页面导航栏配置：
  - AdCenter和Plans页面没有返回按钮
  - Plans下的所有页面都没有铃铛按钮
  - Message页面没有铃铛按钮
  - Service页面没有铃铛按钮

### 移动端适配
- 项目使用 rem 单位进行移动端适配，基于 375px 设计稿
- HTML 根元素字体大小通过 `font-size: calc(100vw / 3.75)` 计算
- 设计稿元素尺寸转换为 rem：设计稿尺寸(px) ÷ 100 = rem 值
- 例如：设计稿元素宽度为 180px，在 CSS 中写为 1.8rem

### 样式规范
- 项目已引入 reset.css，统一浏览器样式
- 全局样式在 src/style.css 文件中定义
- 组件样式使用 scoped 属性限制作用域
- 颜色值和字体大小尽量使用变量管理
- 内边距统一使用 0.1rem 或 0.16rem (16px)
- 色调使用facebook主色调 #1877F2
- 交互功能如果vant中有符合的组件尽量使用vant组件库

### 国际化
- 语言文件位于 src/i18n/locales 目录
- 按功能模块拆分语言文件，易于维护
- 在组件中使用 `t('key')` 的方式引用翻译文本，例如 `t('home')` 或 `t('agent.rules.rule1')`

### 页面标题管理
- 使用 useTitle 工具函数设置页面标题
- 可以传入静态字符串或计算属性函数

### 用户信息管理
- 使用useUserInfo工具函数获取和管理用户信息
- 用户信息存储在Pinia store中，同时会缓存到localStorage
- 支持通过URL参数data进行登录验证
- token存储在localStorage中，并在请求时自动添加到请求头

### 请求封装
- 使用axios进行请求封装
- 自动添加token到请求头
- 统一处理响应错误，包括401未授权错误
- 接口返回格式统一处理，只返回data部分

### 代码规范
- 组件命名使用 PascalCase 格式
- 文件夹和文件名使用小写字母和连字符 (kebab-case)
- 接口请求统一在 src/api 目录中管理
- 使用 async/await 处理异步操作
- 导航跳转使用 useNavigation 工具函数
- 用户信息管理使用 useUserInfo 工具函数
- 页面中的navbar下的整体内容元素使用.content类名

### 性能优化
- 路由懒加载，减少首屏加载时间
- 大型组件拆分为小组件，提高复用性和可维护性
- 使用keep-alive缓存频繁访问的组件
- 图片资源按需加载，减少初始加载体积




当用户点击提现时

验证有没有密码 （isSetPwd）
 无密码 
  设置密码
 有密码 
  验证有没有绑定银行卡
    无
      则提示绑定银行卡 or 虚拟货币
    有
      提现的时候选择 银行卡



字典：

代金券
    isNewComer
      0 非新人
      1 新人
    type
      0  折扣券 
      1  减免券    
    
计划状态
      0
      1
      2
      3
      4
      5
      6      

交易记录类型

跟随状态
  status
    1 active: '跟随中',
    2 ended: '已结束',
    0 pending: '审核中',
