<template>
  <div>
    <!-- 骨架屏 -->
    <template v-if="isLoading">
      <div class="plan-item skeleton-wrapper" v-for="i in 3" :key="i">
        <div class="plan-header">
          <van-skeleton title :row="0" title-width="30%" />
          <van-skeleton title :row="0" title-width="20%" />
        </div>

        <van-skeleton title :row="0" title-width="60%" class="skeleton-margin" />

        <div class="plan-content">
          <van-skeleton avatar avatar-size="0.6rem" />
          <div class="plan-info">
            <van-skeleton title :row="2" />
          </div>
        </div>

        <van-skeleton title :row="1" class="skeleton-margin" />

        <div class="plan-data">
          <van-skeleton title :row="2" />
        </div>

        <van-skeleton title :row="0" title-width="50%" class="skeleton-margin" />
      </div>
    </template>

    <!-- 错误状态 -->
    <template v-else-if="hasError">
      <div class="error-container">
        <van-empty :description="t('fetchFailed')" />
        <van-button type="primary" block @click="handleRetry">{{ t('retry') }}</van-button>
      </div>
    </template>

    <!-- 空数据状态 -->
    <template v-else-if="plans.length === 0">
      <div class="empty-container">
        <van-empty :description="t('noMoreData')" />
      </div>
    </template>

    <!-- 计划列表 -->
    <template v-else>
      <van-list v-model:loading="loading" :finished="finished" :finished-text="t('noMoreData')"
        :loading-text="t('loading')" @load="onLoad">
        <div class="plan-item" v-for="plan in displayPlans" :key="plan.plan_code" @click="goToPlanDetail(plan.id)">
          <div class="plan-header">
            <div class="plan-tag">{{ plan.isOfficial ? t('plans.official') : t('plans.unofficial') }}
            </div>
            <van-tag :color="getStatusColor(plan.status)" :text-color="getStatusTextColor(plan.status)" plain>
              {{ $t('plans.status.' + mapStatusToKey(plan.status)) }}
            </van-tag>
          </div>

          <div class="plan-id">{{ t('plans.planId') }}: {{ plan.plan_code }}</div>

          <div class="plan-content">
            <div class="plan-preview">
              <img :src="plan.appBannerUrl" alt="应用图标">
            </div>
            <div class="plan-right">
              <div class="plan-top">
                <div class="plan-image">
                  <img :src="plan.appIconUrl" alt="应用图标">
                </div>
                <div class="plan-info">
                  <div class="plan-title">{{ plan.name }}</div>
                  <div class="plan-subtitle">{{ plan.category }}</div>
                </div>
              </div>
              <div class="plan-tags">
                <span class="tag">{{ t('plans.video') }}</span>
                <span class="tag">{{ t('plans.landingPage') }}</span>
              </div>
            </div>
          </div>

          <div class="plan-description">{{ plan.description }}</div>

          <!-- 待投放状态 -->
          <template v-if="plan.status === '待投放'">
            <div class="plan-data">
              <div class="data-item">
                <div class="data-label">{{ t('plans.minAmount') }}</div>
                <div class="data-value">${{ plan.minPrice }}</div>
              </div>
              <div class="data-item">
                <div class="data-label">{{ t('plans.maxAmount') }}</div>
                <div class="data-value">${{ plan.maxPrice }}</div>
              </div>
            </div>
            <div class="plan-time">{{ t('plans.createTime') }}: {{ plan.createTime }}</div>
            <div class="plan-action">
              <van-button type="primary" block @click.stop="showPlacementSheet(plan)">{{
                t('plans.startDelivery') }}</van-button>
            </div>
          </template>

          <!-- 匹配中状态 -->
          <template v-else-if="plan.status === '匹配中'">
            <div class="plan-data">
              <div class="data-item">
                <div class="data-label">{{ t('plans.amount') }}</div>
                <div class="data-value">${{ plan.invested_amount }}</div>
              </div>
              <div class="data-item">
                <div class="data-label">{{ t('plans.progress') }}</div>
                <div class="data-value">{{ plan.progress }}</div>
              </div>
            </div>
            <div class="plan-time">{{ t('plans.createTime') }}: {{ plan.createTime }}</div>
          </template>

          <!-- 投放中状态 -->
          <template v-else-if="plan.status === '投放中'">
            <div class="plan-data">
              <div class="data-item">
                <div class="data-label">{{ t('plans.amount') }}</div>
                <div class="data-value">${{ plan.invested_amount }}</div>
              </div>
              <div class="data-item">
                <div class="data-label">{{ t('plans.progress') }}</div>
                <div class="data-value">{{ plan.progress }}</div>
              </div>
            </div>

            <div class="plan-progress">
              <van-progress :percentage="getProgressPercentage(plan.progress)" :show-pivot="false" color="#1877F2" />
            </div>

            <div class="price-info">
              <div class="price-item">
                <div class="price-label">{{ t('plans.normalClickPrice') }}:</div>
                <div class="price-value">{{ plan.vipPrices[0].price }}</div>
              </div>
            </div>

            <div class="vip-price-info" @click.stop="goToVipPage">
              <div class="vip-label">{{ t('plans.vipClickPrice') }}</div>
              <div class="vip-prices">
                <template v-for="(vip, index) in plan.vipPrices" :key="index">
                  <div class="vip-price-item" :class="{ active: vip.is_current }" v-if="vip.level != 'VIP0'">
                    <div class="vip-level">
                      <van-icon name="diamond" :color="getVipColor(index)" />
                      {{ vip.level }}
                    </div>
                    <div class="vip-price">{{ vip.price }}</div>
                  </div>
                </template>
              </div>
            </div>

            <div class="plan-time">{{ t('plans.createTime') }}: {{ plan.createTime }}</div>
          </template>

          <!-- 投放失败状态 -->
          <template v-else-if="plan.status === '投放失败'">
            <div class="plan-data">
              <div class="data-item">
                <div class="data-label">{{ t('plans.amount') }}</div>
                <div class="data-value">${{ plan.invested_amount }}</div>
              </div>
              <div class="data-item">
                <div class="data-label">{{ t('plans.progress') }}</div>
                <div class="data-value">{{ plan.progress }}</div>
              </div>
            </div>
            <div class="plan-time">{{ t('plans.createTime') }}: {{ plan.createTime }}</div>
          </template>

          <!-- 投放完成状态 -->
          <template v-else-if="plan.status === '投放完成'">
            <div class="plan-data">
              <div class="data-item">
                <div class="data-label">{{ t('plans.amount') }}</div>
                <div class="data-value">${{ plan.invested_amount }}</div>
              </div>
              <div class="data-item">
                <div class="data-label">{{ t('plans.progress') }}</div>
                <div class="data-value">{{ plan.progress }}</div>
              </div>
            </div>

            <div class="plan-progress">
              <van-progress :percentage="getProgressPercentage(plan.progress)" :show-pivot="false" color="#1877F2" />
            </div>

            <div class="price-info">
              <div class="price-item">
                <div class="price-label">{{ t('plans.normalClickPrice') }}:</div>
                <div class="price-value">{{ plan.vipPrices[0].price }}</div>
              </div>
            </div>

            <div class="vip-price-info" @click.stop="goToVipPage">
              <div class="vip-label">{{ t('plans.vipClickPrice') }}</div>
              <div class="vip-prices">
                <template v-for="(vip, index) in plan.vipPrices" :key="index">
                  <div class="vip-price-item" :class="{ active: vip.is_current }" v-if="vip.level != 'VIP0'">
                    <div class="vip-level">
                      <van-icon name="diamond" :color="getVipColor(index)" />
                      {{ vip.level }}
                    </div>
                    <div class="vip-price">{{ vip.price }}</div>
                  </div>
                </template>
              </div>
            </div>

            <div class="plan-time">{{ t('plans.createTime') }}: {{ plan.createTime }}</div>
          </template>
        </div>
      </van-list>
    </template>
  </div>

  <!-- 投放金额弹出层 -->
  <van-action-sheet v-model:show="placementVisible" :close-on-click-overlay="true">
    <div class="placement-container">
      <div class="placement-title">{{ t('plans.placementAmount') }}</div>
      <div class="placement-input-area">
        <div class="input-label">{{ t('plans.enterAmount') }}</div>
        <div class="input-container">
          <span class="currency-symbol">$</span>
          <input type="number" v-model="placementAmount" class="placement-input"
            :placeholder="`${currentPlan?.minPrice || '0.00'} - ${currentPlan?.maxPrice || '500.00'}`" />
          <van-icon v-if="placementAmount" name="clear" class="clear-icon" @click="placementAmount = ''" />
        </div>
        <div class="available-amount">
          <span>{{ t('wallet.availableBalance') }}: ${{ userStore.userInfo.balance || '0.00' }}</span>
          <span class="use-all" @click="useAllBalance">{{ t('useAll') }}</span>
        </div>

        <!-- 优惠券单元格 -->
        <div class="coupon-cell" @click="showCouponList">
          <div class="coupon-cell-left">
            <span>{{ t('plans.coupon.title') }}</span>
          </div>
          <div class="coupon-cell-right">
            <span v-if="chosenCoupon === -1" class="coupon-placeholder">{{ t('plans.coupon.notUse') }}</span>
            <span v-else class="coupon-selected">-{{coupons.find(item => item.id === chosenCoupon)?.type === 0 ? "%" :
              "$"}}{{coupons.find(item => item.id === chosenCoupon)?.value - 0 || 0
              }}</span>
            <van-icon name="arrow" />
          </div>
        </div>
      </div>
      <div class="placement-btn" @click="confirmPlacement">{{ t('plans.confirm') }}</div>
    </div>
  </van-action-sheet>

  <!-- 优惠券列表弹窗 -->
  <van-action-sheet v-model:show="couponVisible" :close-on-click-overlay="true">
    <div class="coupon-list-container">
      <div class="coupon-list-header">
        <span>{{ t('plans.coupon.title') }}</span>
        <van-icon name="cross" @click="couponVisible = false" />
      </div>

      <!-- 加载中状态 -->
      <div class="loading-container" v-if="loadingCoupons">
        <van-loading type="spinner" color="#1877F2" />
        <span class="loading-text">{{ t('loading') }}</span>
      </div>

      <!-- 优惠券列表 -->
      <div class="coupon-list" v-else>
        <template v-if="coupons.length > 0">
          <div class="coupon-item" v-for="(coupon, index) in coupons" :key="index" @click="onSelectCoupon(coupon)">
            <div class="coupon-left">
              <div class="coupon-amount">
                <span class="currency">{{ coupon.type === 0 ? "%" : "$" }}</span>
                <span class="value">{{ formatValue(coupon) }}</span>
              </div>
              <div class="coupon-threshold">{{ (coupon.minSpend - 0) !== 0 ? t('coupon.minConsumption',
                { amount: formatMinSpend(coupon.minSpend) }) : t('coupon.nolimit') }}</div>
            </div>
            <div class="coupon-right">
              <div class="coupon-name">{{ coupon.name }}</div>
              <div class="coupon-time">{{ t('plans.coupon.validity') }} {{ new
                Date(coupon.endTime).toLocaleDateString() }}</div>
              <div class="coupon-desc">{{ coupon.description }}</div>
            </div>
            <div class="coupon-check" v-if="chosenCoupon === coupon.id">
              <van-icon name="success" color="#1877F2" size="18" />
            </div>
          </div>
        </template>

        <!-- 没有可用优惠券 -->
        <van-empty v-else :description="t('plans.coupon.empty')" />

        <div class="no-coupon" @click="notUseCoupon">
          {{ t('plans.coupon.notUse') }}
        </div>
      </div>
    </div>
  </van-action-sheet>

  <!-- 交易密码验证组件 -->
  <transaction-password-verify v-model:show="showPasswordVerify" @confirm="onPasswordConfirm" @cancel="onPasswordCancel"
    @verification-error="onVerificationError" ref="passwordVerifyRef" />
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import {
  Button as VanButton,
  Progress as VanProgress,
  List as VanList,
  Icon as VanIcon,
  Tag as VanTag,
  ActionSheet as VanActionSheet,
  Skeleton as VanSkeleton,
  Empty as VanEmpty,
  showToast,
  CouponCell as VanCouponCell,
  CouponList as VanCouponList,
  Loading as VanLoading
} from 'vant'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { couponApi, planApi } from '@/api'
import TransactionPasswordVerify from '@/components/TransactionPasswordVerify.vue'

const { t } = useI18n()
const router = useRouter()
const userStore = useUserStore()

const props = defineProps({
  plans: {
    type: Array,
    required: true
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  hasError: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['retry'])

const loading = ref(false)
const finished = ref(true)
const displayPlans = ref([])

// 投放相关数据
const placementVisible = ref(false)
const placementAmount = ref('')
const currentPlan = ref(null)

// 优惠券相关数据
const couponVisible = ref(false)
const chosenCoupon = ref(-1)
const coupons = ref([])
const loadingCoupons = ref(false)

// 交易密码验证相关数据
const showPasswordVerify = ref(false)
const passwordVerifyRef = ref(null)
const pendingPlacementData = ref(null)

// 监听props.plans的变化，更新displayPlans
watch(() => props.plans, (newPlans) => {
  if (newPlans && newPlans.length > 0) {
    displayPlans.value = newPlans
  } else {
    displayPlans.value = []
  }
}, { immediate: true, deep: true })

// 格式化金额显示
const formatValue = (item) => {
  console.log(item)
  if (item.type === 0) {
    return parseFloat(item.value - 0) // 百分比显示，例如0.8显示为8折
  }
  return item.value
}

// 格式化最低消费
const formatMinSpend = (minSpend) => {
  return parseFloat(minSpend).toLocaleString()
}

// 格式化到期日期
const formatExpireDate = (endTime) => {
  if (!endTime) return ''
  return new Date(endTime).toLocaleDateString()
}

// 处理重试事件
const handleRetry = () => {
  emit('retry')
}

// 显示投放金额弹出层
const showPlacementSheet = (plan) => {
  currentPlan.value = plan
  placementVisible.value = true
  chosenCoupon.value = -1 // 重置优惠券选择
}

// 显示优惠券列表
const showCouponList = () => {
  if (coupons.value.length === 0) {
    loadAvailableCoupons()
  }
  couponVisible.value = true
}

// 加载可用优惠券
const loadAvailableCoupons = async () => {
  if (loadingCoupons.value) return

  loadingCoupons.value = true
  try {
    const result = await couponApi.fetchAvailableCoupons({
      page: 1,
      pageSize: 10
    })

    if (result && result.code === 200 && result.data) {
      // 如果返回的是分页数据结构
      if (result.data.list) {
        coupons.value = result.data.list || []
      } else {
        coupons.value = result.data || []
      }
    } else {
      coupons.value = []
      showToast(t('error.loadFailed'))
    }
  } catch (error) {
    console.error('获取可用优惠券失败:', error)
    showToast(t('error.loadFailed'))
    coupons.value = []
  } finally {
    loadingCoupons.value = false
  }
}

// 选择优惠券
const onSelectCoupon = (coupon) => {
  chosenCoupon.value = coupon.id
  couponVisible.value = false
}

// 不使用优惠券
const notUseCoupon = () => {
  chosenCoupon.value = -1
  couponVisible.value = false
}

// 确认投放
const confirmPlacement = async () => {
  if (!placementAmount.value || parseFloat(placementAmount.value) <= 0) {
    showToast(t('plans.invalidAmount'))
    return
  }

  const minAmount = parseFloat(currentPlan.value.minPrice)
  if (parseFloat(placementAmount.value) < minAmount) {
    showToast(t('plans.amountTooLow'))
    return
  }

  // 根据后端数据格式，这里使用 maxPrice 作为最大金额
  const maxAmount = parseFloat(currentPlan.value.maxPrice)
  if (parseFloat(placementAmount.value) > maxAmount) {
    showToast(t('plans.amountTooHigh'))
    return
  }

  // 如果选择了优惠券，调用使用优惠券接口
  let finalAmount = parseFloat(placementAmount.value)


  // 保存待处理的投放数据
  pendingPlacementData.value = {
    planId: currentPlan.value.id || currentPlan.value.plan_code,
    amount: finalAmount,
    userId: userStore.userInfo.id
  }

  // 显示交易密码验证弹窗
  showPasswordVerify.value = true
}

// 密码验证成功回调
const onPasswordConfirm = async (password) => {
  if (!pendingPlacementData.value) return

  try {
    // 调用投放计划API，并传递交易密码

    const result = await planApi.launchPlan(pendingPlacementData.value.planId, {
      ...pendingPlacementData.value,
      couponId: chosenCoupon.value,
      payPassword: password
    })

    if (result && result.code === 200) {
      showToast(t('plans.placementSuccess'))
      // 关闭密码验证弹窗
      passwordVerifyRef.value?.closeVerify(true)
      // 关闭投放弹窗
      placementVisible.value = false
      // 重置数据
      placementAmount.value = ''
      chosenCoupon.value = -1
      pendingPlacementData.value = null

      // 触发重试事件，刷新计划列表
      emit('retry')
    } else {
      showToast(result?.message || t('plans.placementFailed'))
      // 关闭密码验证弹窗，但不标记为成功
      passwordVerifyRef.value?.closeVerify(false)
    }
  } catch (error) {
    console.error('投放计划失败:', error)
    showToast(error.message || t('plans.placementFailed'))
    // 关闭密码验证弹窗，但不标记为成功
    passwordVerifyRef.value?.closeVerify(false)
  }
}

// 密码验证取消回调
const onPasswordCancel = () => {
  pendingPlacementData.value = null
}

// 密码验证错误回调
const onVerificationError = ({ message }) => {
  console.error('密码验证失败:', message)
  // 错误处理已在组件内部完成，这里可以添加额外的处理逻辑
}

// 加载更多计划
const onLoad = () => {
  // 开启加载状态
  loading.value = true

  // 模拟API请求延迟
  setTimeout(() => {
    // 实际项目中这里应该发送请求获取更多数据
    loading.value = false
    finished.value = true
  }, 1500)
}

// 处理进度百分比
const getProgressPercentage = (progress) => {
  if (typeof progress === 'string') {
    // 处理 "25%" 格式的字符串
    return parseInt(progress.replace('%', '')) || 0
  }
  return progress || 0
}

// 将中文状态值映射为英文键值
const mapStatusToKey = (status) => {
  const statusMap = {
    '待投放': 'pending',
    '匹配中': 'matching',
    '投放中': 'running',
    '投放完成': 'completed',
    '等待结算': 'pending_settlement',
    '请完成全部订单': 'complete_all_orders',
    '结算成功': 'settlement_success',
    '投放失败': 'failed'
  }
  return statusMap[status] || status
}

// 获取状态对应的背景颜色
const getStatusColor = (status) => {
  const statusColorMap = {
    // 中文状态值（向后兼容）
    '待投放': '#1877F2',
    '匹配中': '#1877F2',
    '投放中': '#1877F2',
    '投放完成': '#52c41a',
    '等待结算': '#faad14',
    '请完成全部订单': '#fa8c16',
    '结算成功': '#52c41a',
    '投放失败': '#ff4d4f',
    // 英文状态值
    'pending': '#1877F2',
    'matching': '#1877F2',
    'running': '#1877F2',
    'completed': '#52c41a',
    'pending_settlement': '#faad14',
    'complete_all_orders': '#fa8c16',
    'settlement_success': '#52c41a',
    'failed': '#ff4d4f'
  }
  return statusColorMap[status] || '#1877F2'
}

// 获取状态对应的文本颜色
const getStatusTextColor = (status) => {
  const statusTextColorMap = {
    // 中文状态值（向后兼容）
    '待投放': '#1877F2',
    '匹配中': '#1877F2',
    '投放中': '#1877F2',
    '投放完成': '#52c41a',
    '等待结算': '#faad14',
    '请完成全部订单': '#fa8c16',
    '结算成功': '#52c41a',
    '投放失败': '#ff4d4f',
    // 英文状态值
    'pending': '#1877F2',
    'matching': '#1877F2',
    'running': '#1877F2',
    'completed': '#52c41a',
    'pending_settlement': '#faad14',
    'complete_all_orders': '#fa8c16',
    'settlement_success': '#52c41a',
    'failed': '#ff4d4f'
  }
  return statusTextColorMap[status] || '#1877F2'
}

// 获取VIP等级对应的颜色
const getVipColor = (index) => {
  const colors = ['#1877F2', '#FFD700', '#FFD700', '#FFD700']
  return colors[index] || '#1877F2'
}

// 跳转到计划详情页面
const goToPlanDetail = (planId) => {
  router.push(`/plans/plan/${planId}`)
}

// 跳转到VIP页面
const goToVipPage = () => {
  // 不再需要阻止事件冒泡，因为我们在模板中使用了 @click.stop
  router.push('/adcenter/vip')
}

// 使用全部余额
const useAllBalance = () => {
  const userBalance = parseFloat(userStore.userInfo.balance || 0);
  const maxAmount = parseFloat(currentPlan.value?.maxPrice || 500);
  // 使用用户余额和最大投放金额中的较小值
  placementAmount.value = Math.min(userBalance, maxAmount).toString();
}
</script>

<style scoped>
.plan-item {
  background-color: #fff;
  border-radius: 0.1rem;
  margin: 0.16rem;
  padding: 0.16rem;
  box-shadow: 0 0.02rem 0.1rem rgba(0, 0, 0, 0.05);
  position: relative;
  cursor: pointer;
}

.plan-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.1rem;
  align-items: center;
}

.plan-tag {
  background-color: #1877F2;
  color: white;
  font-size: 0.12rem;
  padding: 0.04rem 0.08rem;
  border-radius: 0.04rem;
}

.plan-id {
  font-size: 0.14rem;
  color: #666;
  margin-bottom: 0.1rem;
}

.plan-content {
  display: flex;
  margin-bottom: 0.1rem;
}

.plan-preview {
  width: 3.4rem;
  height: 1.02rem;
  border-radius: 0.1rem;
  overflow: hidden;
  margin-right: 0.1rem;
}

.plan-top {
  display: flex;
}

.plan-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.plan-image {
  width: 0.36rem;
  height: 0.36rem;
  border-radius: 0.1rem;
  overflow: hidden;
  margin-right: 0.1rem;
}

.plan-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.plan-info {
  flex: 1;
}

.plan-title {
  font-size: 0.14rem;
  font-weight: bold;
  margin-bottom: 0.04rem;
}

.plan-subtitle {
  font-size: 0.12rem;
  color: #666;
  margin-bottom: 0.04rem;
}

.plan-tags {
  display: flex;
}

.tag {
  font-size: 0.12rem;
  padding: 0.02rem 0.06rem;
  background-color: #f5f5f5;
  border-radius: 0.04rem;
  margin-right: 0.06rem;
}

.plan-description {
  font-size: 0.14rem;
  color: #333;
  margin-bottom: 0.1rem;
  line-height: 1.4;
}

.plan-data {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.1rem;
}

.data-item {
  text-align: center;
  flex: 1;
}

.data-label {
  font-size: 0.12rem;
  color: #666;
}

.data-value {
  font-size: 0.16rem;
  font-weight: bold;
  color: #333;
}

.plan-time {
  font-size: 0.12rem;
  color: #999;
  margin-bottom: 0.1rem;
}

.plan-action {
  margin-top: 0.1rem;
}

.plan-progress {
  margin: 0.1rem 0;
}

.price-info {
  margin: 0.1rem 0;
}

.price-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.14rem;
}

.price-label {
  color: #666;
}

.price-value {
  font-weight: bold;
}

.vip-price-info {
  margin: 0.1rem 0;
  background-color: #f5f5f5;
  border-radius: 0.08rem;
  padding: 0.1rem;
}

.vip-label {
  font-size: 0.14rem;
  margin-bottom: 0.1rem;
}

.vip-prices {
  display: flex;
  justify-content: space-between;
  justify-content: flex-start;
}

.vip-price-item {
  text-align: center;
  padding: 0.08rem;
  border-radius: 0.06rem;
}

.vip-price-item.active {
  background-color: #e6f7ff;
  border: 1px solid #1877F2;
}

.vip-level {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.12rem;
  margin-bottom: 0.04rem;
}

.vip-level .van-icon {
  margin-right: 0.04rem;
}

.vip-price {
  font-size: 0.14rem;
  font-weight: bold;
}

/* 骨架屏样式 */
.skeleton-wrapper {
  overflow: hidden;
}

.skeleton-margin {
  margin: 0.1rem 0;
}

.skeleton-button {
  margin-top: 0.2rem;
  height: 0.44rem;
}

/* 错误和空状态容器 */
.error-container,
.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 200px;
  padding: 0.16rem;
}

.error-container .van-button {
  margin-top: 0.16rem;
  width: 80%;
}

/* 投放金额弹出层样式 */
.placement-container {
  padding: 0.2rem 0.16rem 0.3rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.placement-title {
  font-size: 0.18rem;
  font-weight: bold;
  margin-bottom: 0.3rem;
  text-align: center;
}

.placement-input-area {
  width: 100%;
  margin-bottom: 0.3rem;
}

.input-label {
  font-size: 0.14rem;
  color: #333;
  margin-bottom: 0.2rem;
  text-align: center;
}

.input-container {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #ddd;
  padding-bottom: 0.1rem;
  margin-bottom: 0.1rem;
  position: relative;
}

.currency-symbol {
  font-size: 0.24rem;
  font-weight: bold;
  margin-right: 0.08rem;
  color: #333;
}

.placement-input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 0.24rem;
  font-weight: bold;
  padding-right: 0.3rem;
}

.clear-icon {
  position: absolute;
  right: 0;
  color: #999;
  font-size: 0.18rem;
  padding: 0.05rem;
}

.available-amount {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.12rem;
  color: #666;
  padding: 0.05rem 0;
}

.use-all {
  color: #1877F2;
  font-weight: bold;
  cursor: pointer;
}

.placement-btn {
  width: 80%;
  height: 0.44rem;
  background-color: #1877F2;
  color: white;
  font-size: 0.16rem;
  border-radius: 0.22rem;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

/* 优惠券列表弹窗样式 */
.coupon-list-container {
  padding: 0.2rem 0.16rem 0.16rem;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-height: 85vh;
  overflow-y: auto;
}

.coupon-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 0 0.16rem 0.16rem;
  margin-bottom: 0.1rem;
  border-bottom: 1px solid #f5f5f5;
}

.coupon-list-header span {
  font-size: 0.16rem;
  font-weight: bold;
}

.coupon-list {
  width: 100%;
}

.coupon-item {
  display: flex;
  margin: 0.16rem 0;
  border-radius: 0.08rem;
  background: #fff;
  box-shadow: 0 0 0.08rem rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
  height: 1.2rem;
}

.coupon-left {
  width: 1.2rem;
  background-color: #1877F2;
  color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.16rem;
  position: relative;
}

.coupon-left::after {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 0.08rem;
  background-image: radial-gradient(circle at 0 0.08rem, transparent 0.08rem, #fff 0);
  background-size: 0.08rem 0.16rem;
  background-repeat: repeat-y;
}

.coupon-amount {
  font-weight: bold;
  display: flex;
  align-items: flex-end;
}

.currency {
  font-size: 0.2rem;
  margin-bottom: 0.04rem;
}

.value {
  font-size: 0.4rem;
  line-height: 0.4rem;
}

.coupon-threshold {
  font-size: 0.12rem;
  margin-top: 0.08rem;
}

.coupon-right {
  flex: 1;
  padding: 0.16rem;
  position: relative;
}

.coupon-check {
  position: absolute;
  right: 0.16rem;
  top: 50%;
  transform: translateY(-50%);
}

.coupon-name {
  font-size: 0.16rem;
  font-weight: bold;
  margin-bottom: 0.08rem;
}

.coupon-time {
  font-size: 0.12rem;
  color: #999;
  margin-bottom: 0.08rem;
}

.coupon-desc {
  font-size: 0.12rem;
  color: #666;
  padding-right: 0.4rem;
}

.no-coupon {
  text-align: center;
  color: #fff;
  font-size: 0.14rem;
  margin: 0.3rem 0.16rem;
  padding: 0.12rem 0;
  background-color: #1877F2;
  border-radius: 0.22rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
}

.no-coupon:active {
  background-color: #1565c0;
}

/* 优惠券单元格样式 */
.coupon-cell {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.16rem 0;
  margin-top: 0.1rem;
  border-top: 1px solid #f5f5f5;
  cursor: pointer;
  height: 0.44rem;
  background-color: #fff;
  position: relative;
}

.coupon-cell::after {
  position: absolute;
  box-sizing: border-box;
  content: ' ';
  pointer-events: none;
  right: 0.16rem;
  bottom: 0;
  left: 0.16rem;
  border-bottom: 1px solid #ebedf0;
  transform: scaleY(0.5);
}

.coupon-cell-left {
  display: flex;
  align-items: center;
  color: #323233;
  font-size: 0.14rem;
  line-height: 0.24rem;
}

.coupon-icon {
  color: #1877F2;
  margin-right: 0.08rem;
  font-size: 0.18rem;
}

.coupon-cell-right {
  display: flex;
  align-items: center;
  color: #969799;
  font-size: 0.14rem;
}

.coupon-placeholder {
  color: #969799;
  margin-right: 0.05rem;
}

.coupon-selected {
  color: #1877F2;
  font-weight: bold;
  margin-right: 0.05rem;
}

/* 加载中状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.2rem;
}

.loading-text {
  margin-top: 0.2rem;
  color: #1877F2;
  font-size: 0.14rem;
}
</style>