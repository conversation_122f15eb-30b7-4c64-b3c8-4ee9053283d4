<template>
  <!-- TikTok风格佣金中心页面 -->
  <div class="tiktok-commission-container">
    <!-- 顶部导航 -->
    <div class="tiktok-header">
      <div class="header-left">
        <van-icon name="arrow-left" class="back-icon" @click="goBack" />
      </div>
      <div class="header-center">
        <span class="page-title">{{ t('commission.center') }}</span>
      </div>
      <div class="header-right">
        <van-icon name="ellipsis" class="menu-icon" />
      </div>
    </div>

    <div class="tiktok-content">
      <!-- TikTok风格佣金概览 -->
      <div class="tiktok-commission-overview">
        <div class="overview-background">
          <div class="overview-glow"></div>
          <div class="floating-coins">
            <div class="coin" v-for="i in 8" :key="i"></div>
          </div>
        </div>
        
        <div class="overview-content">
          <!-- 主标题 -->
          <div class="overview-header">
            <div class="overview-title">
              <van-icon name="gold-coin-o" class="title-icon" />
              <span>佣金收益</span>
            </div>
            <div class="overview-subtitle">Commission Earnings</div>
          </div>

          <!-- 佣金数据网格 -->
          <div class="commission-grid">
            <div class="commission-card total-card">
              <div class="card-background"></div>
              <div class="card-content">
                <div class="card-icon">
                  <van-icon name="balance-list-o" />
                </div>
                <div class="card-info">
                  <div class="card-label">{{ t('commission.total') }}</div>
                  <div class="card-value">$0.00</div>
                </div>
                <div class="card-glow"></div>
              </div>
            </div>

            <div class="commission-card today-card">
              <div class="card-background"></div>
              <div class="card-content">
                <div class="card-icon">
                  <van-icon name="calendar-o" />
                </div>
                <div class="card-info">
                  <div class="card-label">{{ t('commission.today') }}</div>
                  <div class="card-value">$0.00</div>
                </div>
                <div class="card-glow"></div>
              </div>
            </div>

            <div class="commission-card week-card">
              <div class="card-background"></div>
              <div class="card-content">
                <div class="card-icon">
                  <van-icon name="chart-trending-o" />
                </div>
                <div class="card-info">
                  <div class="card-label">{{ t('commission.thisWeek') }}</div>
                  <div class="card-value">$0.00</div>
                </div>
                <div class="card-glow"></div>
              </div>
            </div>

            <div class="commission-card month-card">
              <div class="card-background"></div>
              <div class="card-content">
                <div class="card-icon">
                  <van-icon name="gold-coin-o" />
                </div>
                <div class="card-info">
                  <div class="card-label">{{ t('commission.thisMonth') }}</div>
                  <div class="card-value">$0.00</div>
                </div>
                <div class="card-glow"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- TikTok风格团队统计 -->
      <div class="tiktok-team-stats">
        <div class="stats-header">
          <h3 class="stats-title">团队数据</h3>
          <div class="title-glow"></div>
        </div>

        <div class="stats-grid">
          <div class="stats-card team-card">
            <div class="stats-background"></div>
            <div class="stats-content">
              <div class="stats-icon-wrapper">
                <van-icon name="friends-o" class="stats-icon" />
                <div class="icon-pulse"></div>
              </div>
              <div class="stats-info">
                <div class="stats-label">{{ t('commission.teamMembers') }}</div>
                <div class="stats-value">0</div>
              </div>
            </div>
          </div>

          <div class="stats-card direct-card">
            <div class="stats-background"></div>
            <div class="stats-content">
              <div class="stats-icon-wrapper">
                <van-icon name="user-o" class="stats-icon" />
                <div class="icon-pulse"></div>
              </div>
              <div class="stats-info">
                <div class="stats-label">{{ t('commission.directMembers') }}</div>
                <div class="stats-value">0</div>
              </div>
            </div>
          </div>

          <div class="stats-card indirect-card">
            <div class="stats-background"></div>
            <div class="stats-content">
              <div class="stats-icon-wrapper">
                <van-icon name="cluster-o" class="stats-icon" />
                <div class="icon-pulse"></div>
              </div>
              <div class="stats-info">
                <div class="stats-label">{{ t('commission.indirectMembers') }}</div>
                <div class="stats-value">0</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- TikTok风格选项卡 -->
      <div class="tiktok-tabs-container">
        <div class="tabs-header">
          <div class="tab-item" 
               :class="{ active: activeTab === 0 }" 
               @click="activeTab = 0">
            <div class="tab-background"></div>
            <div class="tab-content">
              <van-icon name="friends-o" class="tab-icon" />
              <span class="tab-text">{{ t('commission.teamMembers') }}</span>
            </div>
            <div class="tab-glow"></div>
          </div>
          
          <div class="tab-item" 
               :class="{ active: activeTab === 1 }" 
               @click="activeTab = 1">
            <div class="tab-background"></div>
            <div class="tab-content">
              <van-icon name="balance-list-o" class="tab-icon" />
              <span class="tab-text">{{ t('commission.details') }}</span>
            </div>
            <div class="tab-glow"></div>
          </div>
        </div>

        <div class="tabs-content">
          <!-- 团队成员标签页 -->
          <div v-if="activeTab === 0" class="tab-panel">
            <!-- 筛选器 -->
            <div class="tiktok-filter-section">
              <div class="filter-title">筛选条件</div>
              <div class="filter-dropdown">
                <van-dropdown-menu class="tiktok-dropdown">
                  <van-dropdown-item 
                    v-model="teamFilter" 
                    :options="teamFilterOptions" 
                    class="tiktok-dropdown-item"
                  />
                </van-dropdown-menu>
              </div>
            </div>

            <!-- 团队成员列表 -->
            <div class="members-list">
              <div class="empty-state">
                <div class="empty-icon">
                  <van-icon name="friends-o" />
                </div>
                <div class="empty-title">暂无团队成员</div>
                <div class="empty-subtitle">{{ t('commission.noMoreData') }}</div>
              </div>
            </div>
          </div>

          <!-- 佣金明细标签页 -->
          <div v-if="activeTab === 1" class="tab-panel">
            <!-- 筛选器 -->
            <div class="tiktok-filter-section">
              <div class="filter-title">筛选条件</div>
              <div class="filter-dropdown">
                <van-dropdown-menu class="tiktok-dropdown">
                  <van-dropdown-item
                    v-model="detailsFilter"
                    :options="detailsFilterOptions"
                    class="tiktok-dropdown-item"
                  />
                  <van-dropdown-item
                    v-model="dateRangeVisible"
                    :title="dateRangeTitle"
                    class="tiktok-dropdown-item"
                  >
                    <van-cell
                      :title="t('commission.selectDateRange')"
                      is-link
                      @click="showCalendar = true"
                      class="tiktok-cell"
                    />
                  </van-dropdown-item>
                </van-dropdown-menu>
              </div>
            </div>

            <!-- 佣金明细列表 -->
            <div class="details-list">
              <div class="empty-state">
                <div class="empty-icon">
                  <van-icon name="balance-list-o" />
                </div>
                <div class="empty-title">暂无佣金明细</div>
                <div class="empty-subtitle">{{ t('commission.noMoreData') }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 日期选择日历 -->
    <van-calendar 
      v-model:show="showCalendar" 
      type="range" 
      :min-date="minDate" 
      :max-date="maxDate"
      @confirm="onDateRangeConfirm"
      class="tiktok-calendar"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useTitle } from '@/utils/useTitle'
import { useRouter } from 'vue-router'
import { 
  DropdownMenu as VanDropdownMenu, 
  DropdownItem as VanDropdownItem, 
  Cell as VanCell, 
  Calendar as VanCalendar,
  Icon as VanIcon
} from 'vant'

const { t } = useI18n()
const router = useRouter()

useTitle(() => t('commission.center'))

// 返回上一页
const goBack = () => {
  router.back()
}

// 选项卡状态
const activeTab = ref(0)

// 团队成员筛选配置
const teamFilter = ref('all')
const teamFilterOptions = computed(() => [
  { text: t('commission.all'), value: 'all' },
  { text: t('commission.directMembers'), value: 'direct' },
  { text: t('commission.indirectMembers'), value: 'indirect' }
])

// 佣金明细筛选配置
const detailsFilter = ref('all')
const detailsFilterOptions = computed(() => [
  { text: t('commission.all'), value: 'all' },
  { text: t('commission.directCommission'), value: 'direct' },
  { text: t('commission.indirectCommission'), value: 'indirect' }
])

// 日期范围选择
const showCalendar = ref(false)
const dateRangeVisible = ref('')
const selectedDateRange = ref([])
const minDate = new Date(new Date().getFullYear() - 1, 0, 1)
const maxDate = new Date()

// 日期范围标题
const dateRangeTitle = computed(() => {
  if (selectedDateRange.value && selectedDateRange.value.length === 2) {
    const startDate = formatDate(selectedDateRange.value[0])
    const endDate = formatDate(selectedDateRange.value[1])
    return `${startDate} - ${endDate}`
  }
  return t('commission.dateRange')
})

// 格式化日期
function formatDate(date) {
  if (!date) return ''
  const d = new Date(date)
  return `${d.getMonth() + 1}/${d.getDate()}`
}

// 确认日期范围
function onDateRangeConfirm(dates) {
  selectedDateRange.value = dates
  showCalendar.value = false
}
</script>

<style scoped>
/* TikTok风格佣金中心页面全局样式 */
.tiktok-commission-container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(180deg, #000000 0%, #1a1a1a 30%, #2d1b69 70%, #000000 100%);
  color: #ffffff;
  overflow-x: hidden;
}

/* TikTok风格顶部导航 */
.tiktok-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 0.6rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 0.16rem;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 1000;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-left,
.header-right {
  width: 0.4rem;
  display: flex;
  justify-content: center;
}

.header-center {
  flex: 1;
  text-align: center;
}

.back-icon,
.menu-icon {
  font-size: 0.24rem;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-icon:active,
.menu-icon:active {
  transform: scale(0.9);
  color: #ff0050;
}

.page-title {
  font-size: 0.18rem;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.tiktok-content {
  padding: 0.8rem 0.16rem 0.4rem;
}

/* TikTok风格佣金概览 */
.tiktok-commission-overview {
  position: relative;
  margin-bottom: 0.24rem;
  border-radius: 0.24rem;
  overflow: hidden;
  background: linear-gradient(135deg, #ff0050 0%, #ff4081 50%, #9c27b0 100%);
  box-shadow: 0 12px 40px rgba(255, 0, 80, 0.4);
  min-height: 3.2rem;
}

.overview-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 0, 80, 0.9) 0%, rgba(156, 39, 176, 0.9) 100%);
}

.overview-glow {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
  animation: overviewGlow 4s ease-in-out infinite;
}

@keyframes overviewGlow {
  0%, 100% { transform: rotate(0deg) scale(1); opacity: 0.3; }
  50% { transform: rotate(180deg) scale(1.1); opacity: 0.6; }
}

.floating-coins {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.coin {
  position: absolute;
  width: 6px;
  height: 6px;
  background: rgba(255, 215, 0, 0.8);
  border-radius: 50%;
  animation: coinFloat 8s ease-in-out infinite;
}

.coin:nth-child(1) { top: 15%; left: 10%; animation-delay: 0s; }
.coin:nth-child(2) { top: 25%; left: 80%; animation-delay: 1s; }
.coin:nth-child(3) { top: 45%; left: 15%; animation-delay: 2s; }
.coin:nth-child(4) { top: 65%; left: 75%; animation-delay: 3s; }
.coin:nth-child(5) { top: 35%; left: 50%; animation-delay: 4s; }
.coin:nth-child(6) { top: 75%; left: 30%; animation-delay: 5s; }
.coin:nth-child(7) { top: 55%; left: 90%; animation-delay: 6s; }
.coin:nth-child(8) { top: 85%; left: 60%; animation-delay: 7s; }

@keyframes coinFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); opacity: 0.6; }
  25% { transform: translateY(-15px) rotate(90deg) scale(1.2); opacity: 1; }
  50% { transform: translateY(-30px) rotate(180deg) scale(1); opacity: 0.8; }
  75% { transform: translateY(-15px) rotate(270deg) scale(1.1); opacity: 1; }
}

.overview-content {
  position: relative;
  z-index: 2;
  padding: 0.32rem;
}

.overview-header {
  text-align: center;
  margin-bottom: 0.24rem;
}

.overview-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.08rem;
  font-size: 0.2rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 0.08rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.title-icon {
  font-size: 0.24rem;
  color: #ffd700;
  text-shadow: 0 0 10px rgba(255, 215, 0, 0.6);
  animation: titleIconPulse 2s ease-in-out infinite;
}

@keyframes titleIconPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.overview-subtitle {
  font-size: 0.12rem;
  color: rgba(255, 255, 255, 0.8);
  text-transform: uppercase;
  letter-spacing: 0.02rem;
}

.commission-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.12rem;
}

.commission-card {
  position: relative;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 0.16rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
}

.commission-card:active {
  transform: scale(0.98);
}

.card-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.commission-card:active .card-background {
  opacity: 1;
}

.card-content {
  position: relative;
  z-index: 2;
  padding: 0.16rem;
  display: flex;
  align-items: center;
  gap: 0.12rem;
}

.card-icon {
  width: 0.32rem;
  height: 0.32rem;
  border-radius: 0.08rem;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.16rem;
  color: #ffffff;
}

.card-info {
  flex: 1;
}

.card-label {
  font-size: 0.1rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 0.04rem;
  line-height: 1.2;
}

.card-value {
  font-size: 0.16rem;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.card-glow {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 0.18rem;
  opacity: 0;
  z-index: -1;
  transition: opacity 0.3s ease;
}

.total-card .card-glow {
  background: linear-gradient(45deg, rgba(255, 215, 0, 0.4), rgba(255, 193, 7, 0.4));
}

.today-card .card-glow {
  background: linear-gradient(45deg, rgba(0, 255, 136, 0.4), rgba(76, 175, 80, 0.4));
}

.week-card .card-glow {
  background: linear-gradient(45deg, rgba(33, 150, 243, 0.4), rgba(63, 81, 181, 0.4));
}

.month-card .card-glow {
  background: linear-gradient(45deg, rgba(255, 87, 34, 0.4), rgba(255, 152, 0, 0.4));
}

.commission-card:hover .card-glow {
  opacity: 1;
  animation: cardGlow 2s ease-in-out infinite;
}

@keyframes cardGlow {
  0%, 100% { opacity: 0.4; }
  50% { opacity: 0.8; }
}

/* TikTok风格团队统计 */
.tiktok-team-stats {
  margin-bottom: 0.24rem;
}

.stats-header {
  position: relative;
  margin-bottom: 0.16rem;
}

.stats-title {
  font-size: 0.18rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.title-glow {
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 0.6rem;
  height: 2px;
  background: linear-gradient(90deg, #ff0050, transparent);
  border-radius: 1px;
}

.stats-grid {
  display: flex;
  gap: 0.12rem;
}

.stats-card {
  position: relative;
  flex: 1;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-radius: 0.16rem;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
}

.stats-card:active {
  transform: scale(0.98);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%);
}

.stats-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 0, 80, 0.1), rgba(156, 39, 176, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stats-card:active .stats-background {
  opacity: 1;
}

.stats-content {
  position: relative;
  z-index: 2;
  padding: 0.16rem;
  text-align: center;
}

.stats-icon-wrapper {
  position: relative;
  width: 0.4rem;
  height: 0.4rem;
  border-radius: 0.12rem;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 0.12rem;
}

.stats-icon {
  font-size: 0.2rem;
  color: #ff0050;
}

.icon-pulse {
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border: 2px solid rgba(255, 0, 80, 0.3);
  border-radius: 0.16rem;
  animation: iconPulse 2s ease-in-out infinite;
}

@keyframes iconPulse {
  0%, 100% { opacity: 0.6; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.05); }
}

.stats-info {
  text-align: center;
}

.stats-label {
  font-size: 0.1rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 0.04rem;
  line-height: 1.2;
}

.stats-value {
  font-size: 0.18rem;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* TikTok风格选项卡 */
.tiktok-tabs-container {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 0.16rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

.tabs-header {
  display: flex;
  background: rgba(0, 0, 0, 0.3);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.tab-item {
  position: relative;
  flex: 1;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
}

.tab-item:active {
  transform: scale(0.98);
}

.tab-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 0, 80, 0.2), rgba(156, 39, 176, 0.2));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.tab-item.active .tab-background {
  opacity: 1;
}

.tab-content {
  position: relative;
  z-index: 2;
  padding: 0.16rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.08rem;
}

.tab-icon {
  font-size: 0.16rem;
  color: rgba(255, 255, 255, 0.7);
  transition: all 0.3s ease;
}

.tab-item.active .tab-icon {
  color: #ff0050;
}

.tab-text {
  font-size: 0.14rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
  transition: all 0.3s ease;
}

.tab-item.active .tab-text {
  color: #ffffff;
  font-weight: 600;
}

.tab-glow {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #ff0050, #ff4081);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.tab-item.active .tab-glow {
  opacity: 1;
}

.tabs-content {
  padding: 0.2rem;
}

.tab-panel {
  min-height: 2rem;
}

/* TikTok风格筛选器 */
.tiktok-filter-section {
  margin-bottom: 0.2rem;
}

.filter-title {
  font-size: 0.14rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 0.12rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.filter-dropdown {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 0.12rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.tiktok-dropdown :deep(.van-dropdown-menu) {
  background: transparent;
}

.tiktok-dropdown :deep(.van-dropdown-menu__bar) {
  background: transparent;
  border-bottom: none;
  padding: 0.12rem;
}

.tiktok-dropdown :deep(.van-dropdown-menu__title) {
  color: #ffffff;
  font-size: 0.14rem;
  font-weight: 500;
  padding: 0.08rem 0.12rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0.08rem;
  margin: 0.04rem;
  transition: all 0.3s ease;
}

.tiktok-dropdown :deep(.van-dropdown-menu__title:active) {
  background: rgba(255, 0, 80, 0.2);
  transform: scale(0.98);
}

.tiktok-dropdown :deep(.van-dropdown-menu__title::after) {
  border-color: rgba(255, 255, 255, 0.8);
  margin-left: 0.08rem;
}

.tiktok-dropdown :deep(.van-dropdown-item) {
  background: rgba(0, 0, 0, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
  border-radius: 0.12rem;
  margin-top: 0.08rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
}

.tiktok-dropdown :deep(.van-dropdown-item .van-cell) {
  background: transparent;
  color: #ffffff;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 0.12rem 0.16rem;
  transition: all 0.3s ease;
}

.tiktok-dropdown :deep(.van-dropdown-item .van-cell:last-child) {
  border-bottom: none;
}

.tiktok-dropdown :deep(.van-dropdown-item .van-cell--clickable:active) {
  background: rgba(255, 0, 80, 0.2);
  transform: scale(0.98);
}

.tiktok-cell :deep(.van-cell) {
  background: transparent;
  color: #ffffff;
  padding: 0.12rem 0.16rem;
  transition: all 0.3s ease;
}

.tiktok-cell :deep(.van-cell--clickable:active) {
  background: rgba(255, 0, 80, 0.2);
  transform: scale(0.98);
}

.tiktok-cell :deep(.van-cell__title) {
  color: #ffffff;
  font-size: 0.14rem;
}

.tiktok-cell :deep(.van-cell__right-icon) {
  color: rgba(255, 255, 255, 0.8);
}

/* 空状态样式 */
.members-list,
.details-list {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 0.12rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-state {
  text-align: center;
  padding: 0.4rem;
}

.empty-icon {
  width: 0.8rem;
  height: 0.8rem;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 0.16rem;
  font-size: 0.32rem;
  color: rgba(255, 255, 255, 0.6);
}

.empty-title {
  font-size: 0.16rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 0.08rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.empty-subtitle {
  font-size: 0.12rem;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.4;
}

/* TikTok风格日历样式 */
.tiktok-calendar :deep(.van-calendar) {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.95) 0%, rgba(26, 26, 26, 0.95) 100%);
  border-top-left-radius: 0.2rem;
  border-top-right-radius: 0.2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
}

.tiktok-calendar :deep(.van-calendar__header) {
  background: transparent;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.tiktok-calendar :deep(.van-calendar__header-title) {
  color: #ffffff;
  font-weight: 600;
}

.tiktok-calendar :deep(.van-calendar__weekdays) {
  background: transparent;
}

.tiktok-calendar :deep(.van-calendar__weekday) {
  color: rgba(255, 255, 255, 0.7);
}

.tiktok-calendar :deep(.van-calendar__month-title) {
  color: #ffffff;
  font-weight: 600;
}

.tiktok-calendar :deep(.van-calendar__day) {
  color: #ffffff;
}

.tiktok-calendar :deep(.van-calendar__day--selected) {
  background: #ff0050;
  color: #ffffff;
}

.tiktok-calendar :deep(.van-calendar__day--start) {
  background: #ff0050;
  color: #ffffff;
}

.tiktok-calendar :deep(.van-calendar__day--end) {
  background: #ff0050;
  color: #ffffff;
}

.tiktok-calendar :deep(.van-calendar__day--middle) {
  background: rgba(255, 0, 80, 0.3);
  color: #ffffff;
}

.tiktok-calendar :deep(.van-calendar__confirm) {
  background: linear-gradient(135deg, #ff0050 0%, #ff4081 100%);
  color: #ffffff;
  font-weight: 600;
  border: none;
}
</style>
