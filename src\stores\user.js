import { defineStore } from 'pinia'
import { ref } from 'vue'
import { updateTopPadding } from '@/utils/paddingAdjuster'

// 获取本地存储中的token和userInfo的key
const tokenKey = import.meta.env.VITE_APP_TOKEN_KEY || 'facebook_ad_token'
const userInfoKey = import.meta.env.VITE_APP_USER_INFO_KEY || 'facebook_ad_user_info'

export const useUserStore = defineStore('user', () => {
  // 从localStorage获取token
  const token = ref(localStorage.getItem(tokenKey) || '')

  // 从localStorage获取userInfo，如果没有则为空对象
  let storedUserInfo = {}
  try {
    const userInfoStr = localStorage.getItem(userInfoKey)
    if (userInfoStr) {
      storedUserInfo = JSON.parse(userInfoStr)
    }
  } catch (error) {
    console.error('解析存储的用户信息失败:', error)
  }

  const userInfo = ref(storedUserInfo)
  const isLogin = ref(!!token.value)

  // 设置token
  function setToken(value) {
    token.value = value
    localStorage.setItem(tokenKey, value)
    isLogin.value = true
  }

  // 清除token
  function clearToken() {
    token.value = ''
    localStorage.removeItem(tokenKey)
    isLogin.value = false
  }

  // 设置用户信息
  function setUserInfo(info) {
    userInfo.value = info
    // 将用户信息存储到localStorage
    try {
      localStorage.setItem(userInfoKey, JSON.stringify(info))
      // 更新顶部内边距
      updateTopPadding()
    } catch (error) {
      console.error('存储用户信息失败:', error)
    }
  }

  // 登出
  function logout() {
    clearToken()
    userInfo.value = {}
    // 清除localStorage中的用户信息
    localStorage.removeItem(userInfoKey)
  }

  return {
    token,
    userInfo,
    isLogin,
    setToken,
    clearToken,
    setUserInfo,
    logout
  }
}) 