import axios from 'axios'
import { useUserStore } from '@/stores/user'

// 从环境变量获取基础URL
const baseURL = import.meta.env.VITE_APP_BASE_API || '/api'

const service = axios.create({
  baseURL,
  timeout: 5000
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 获取token并添加到请求头
    const userStore = useUserStore()
    if (userStore.token) {
      config.headers['Authorization'] = `Bearer ${userStore.token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data
    // 判断接口返回是否正常
    if (res.code !== 200) {
      // 可以添加错误处理逻辑
      return Promise.reject(new Error(res.message || '接口请求错误'))
    }
    return res
  },
  error => {
    // 处理401未授权错误，可能是token过期
    if (error.response && error.response.status === 401) {
      // 清除token并跳转到登录页
      const userStore = useUserStore()
      userStore.logout()
      // 路由跳转可以在这里处理
    }
    return Promise.reject(error)
  }
)

export default service 