export default {
  agent: {
    title: 'Plan Following',
    currentAgentId: 'Your Current Agency ID',
    followButton: 'Follow Plan',
    tabs: {
      rules: 'Operation Rules',
      records: 'Follow Records'
    },
    rules: {
      rule1: 'Facebook ad operation rule feature is called "Automated Rules".',
      rule2: 'This is a feature that automatically adjusts operations based on default rules.',
      rule3: 'This way, Facebook ad operation rules can achieve automatic operation adjustments, helping advertisers manage their ad campaigns more efficiently.',
      rule4: 'To establish effective automated rules, you need to infer and understand the ad performance and set appropriate rules.',
      rule5: 'For specific rule settings and usage methods, please consult online customer service or refer to Facebook\'s official guidelines.'
    },
    recordList: {
      noMore: 'No more data',
      following: 'Following',
      active: 'Active',
      ended: 'Ended',
      pending: 'Pending',
      empty: 'No follow records',
      spend: 'Spend'
    },
    actionSheet: {
      title: 'Follow Plan',
      inputLabel: 'Please enter Agency ID',
      placeholder: 'Enter Agency ID',
      confirm: 'Confirm'
    },
    success: {
      followSubmitted: 'Follow request submitted'
    },
    errors: {
      followFailed: 'Failed to submit follow request',
      loadFailed: 'Failed to load follow records'
    }
  }
} 