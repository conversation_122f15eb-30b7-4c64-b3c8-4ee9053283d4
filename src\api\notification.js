import request from '@/utils/request'

/**
 * 获取消息通知列表
 * @param {Object} params - 查询参数
 * @param {String} params.messageType - 消息类型(all, system, maintenance, activity)
 * @param {Number} params.page - 页码
 * @param {Number} params.pageSize - 每页条数
 * @returns {Promise<Object>} - 消息通知列表
 */
export function getNotifications(params) {
  
    // 构建请求参数，确保时间戳存在防止缓存
    const requestParams = {
      ...params,
      _t: Date.now()
    }
    
    return request({
      url: 'api/notification/list',
      method: 'get',
      params: requestParams,
    })
  }
  
  /**
   * 标记单条通知为已读
   * @param {String} notificationId - 通知ID
   * @param {Object} [params] - 附加参数
   * @returns {Promise<Object>} - 操作结果
   */
  export function readNotification(notificationId, params = {}) {
    
    // 构建请求参数
    const requestParams = {
      ...params,
      _t: Date.now()
    }
    
    return request({
      url: `api/notification/read/${notificationId}`,
      method: 'put',
      params: requestParams
    })
  }
  
  /**
   * 标记所有通知为已读
   * @param {Object} [params] - 附加参数
   * @returns {Promise<Object>} - 操作结果
   */
  export function readAllNotifications(params = {}) {
    
    // 构建请求参数
    const requestParams = {
      ...params,
      _t: Date.now()
    }
    
    return request({
      url: 'api/notification/read-all',
      method: 'put',
      params: requestParams
    })
  }
  