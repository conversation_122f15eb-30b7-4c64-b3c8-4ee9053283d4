<template>
  <nav-bar :title="t('feedback.title')" />
  <div class="content">
    <h2>{{ t('feedback.inputPrompt') }}</h2>

    <van-field v-model="title" :placeholder="t('feedback.titlePlaceholder')" class="feedback-field" />

    <van-field v-model="content" type="textarea" :placeholder="t('feedback.contentPlaceholder')" rows="6"
      class="feedback-field" />

    <van-button type="primary" block class="submit-btn" :loading="submitting" @click="handleSubmit">{{
      t('feedback.submit') }}</van-button>
  </div>
</template>

<script setup>
import NavBar from '@/components/NavBar.vue'
import { useTitle } from '@/utils/useTitle'
import { ref } from 'vue'
import { showToast } from 'vant'
import { useI18n } from 'vue-i18n'
import { submitFeedback } from '@/api/feedback'

// 初始化国际化
const { t } = useI18n()

// 设置页面标题
useTitle(t('feedback.title'))

// 表单数据
const title = ref('')
const content = ref('')
const submitting = ref(false)

// 提交反馈
const handleSubmit = async () => {
  if (!title.value.trim()) {
    showToast(t('feedback.emptyTitle'))
    return
  }

  if (!content.value.trim()) {
    showToast(t('feedback.emptyContent'))
    return
  }

  submitting.value = true
  try {
    await submitFeedback({
      title: title.value.trim(),
      content: content.value.trim()
    })
    showToast(t('feedback.submitSuccess'))
    // 清空表单
    title.value = ''
    content.value = ''
  } catch (error) {
    console.error('提交反馈失败:', error)
    showToast(error.message || t('feedback.submitFailed'))
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.content {
  padding: 0.16rem;
}

h2 {
  font-size: 0.16rem;
  margin-bottom: 0.2rem;
  font-weight: normal;
}

.feedback-field {
  margin-bottom: 0.16rem;
  background-color: #f5f5f5;
  border-radius: 0.08rem;
}

.submit-btn {
  margin-top: 0.3rem;
  border-radius: 0.08rem;
  height: 0.44rem;
  font-size: 0.16rem;
}
</style>