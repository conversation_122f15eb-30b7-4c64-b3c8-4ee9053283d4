@import './assets/css/reset.css';

/* 去除滚动条 */
::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Edge */
}

html, body {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
  font-family: Helvetica, Arial, sans-serif;
}

/* 允许内容滚动但隐藏滚动条 */
.content {
  -webkit-overflow-scrolling: touch; /* 为 iOS Safari 提供平滑滚动 */
  margin-top: 46px; /* NavBar 的默认高度 */
}

/* 路由过渡动画相关样式 */
.router-view-container {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
}

/* 确保过渡动画正常工作 */
#app {
  position: relative;
  width: 100%;
  height: 100%;
}

/* 确保页面内容在过渡期间不会溢出 */
.v-enter-active, 
.v-leave-active {
  overflow-x: hidden;
}

:root {
  font-family: Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

h1 {
  font-size: 2.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

.card {
  padding: 2em;
}

/* 确保 NavBar 在顶部固定 */
.van-nav-bar {
  position: fixed !important;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 999;
}

/* 修复底部 Tabbar */
.van-tabbar {
  position: fixed !important;
  bottom: 0;
  left: 0;
  right: 0;
}

/* 防止页面弹性滚动效果（iOS 上常见） */
html {
  width: 100%;
  height: 100%;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}
