/**
 * 用户信息获取工具函数
 * 优先从store中获取，如果store中没有再请求接口
 */
import { useUserStore } from '@/stores/user'
import { fetchUserInfo, loginWithDataParam } from '@/api/adCenter'
import { updateTopPadding } from './paddingAdjuster'
/**
 * 获取用户信息
 * 如果store中已有用户信息则直接返回，否则请求接口获取
 * @param {Boolean} forceUpdate 是否强制更新用户信息，默认false
 * @returns {Promise<Object>} 用户信息对象
 */
export async function getUserInfo(forceUpdate = false) {
  const userStore = useUserStore()
  // 使用.value获取ref对象的实际值
  // 如果store中已有用户信息且不需要强制更新，则直接返回
  if (!forceUpdate && Object.keys(userStore.userInfo).length > 0) {
    return userStore.userInfo
  }

  try {
    // 请求接口获取用户信息
    const response = await fetchUserInfo()
    if (response && response.data) {
      // 将用户信息存储到store中
      userStore.setUserInfo(response.data)
      // 如果用户信息中包含fb_lang字段，将其写入localStorage的locale键中
      if (response.data.fb_lang) {
        localStorage.setItem('locale', response.data.fb_lang)
      }
      // 更新顶部内边距
      updateTopPadding()
      return response.data
    }
    return null
  } catch (error) {
    console.error('获取用户信息失败:', error)
    return null
  }
}

/**
 * 使用URL中的data参数登录并获取用户信息
 * @param {String} dataParam URL中的data参数
 * @returns {Promise<Object>} 用户信息对象
 */
export async function loginAndGetUserInfo(dataParam) {
  if (!dataParam) return null

  const userStore = useUserStore()
  try {
    // 使用data参数登录
    const loginResponse = await loginWithDataParam(dataParam)

    // 如果登录成功，设置token并获取用户信息
    if (loginResponse && loginResponse.data) {
      userStore.setToken(loginResponse.data)
      const userInfo = await getUserInfo(true) // 强制更新用户信息
      // 更新顶部内边距
      updateTopPadding()
      return userInfo
    }
    return null
  } catch (error) {
    console.error('登录并获取用户信息失败:', error)
    return null
  }
} 