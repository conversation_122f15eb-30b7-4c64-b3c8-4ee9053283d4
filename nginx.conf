server {
    listen 80;
    server_name your_domain.com; # 替换为您的实际域名
    root /path/to/your/laravel/public; # 替换为实际的Laravel public目录路径
    index index.html index.php;

    # 主要路由规则
    location / {
        # 首先尝试直接提供请求的文件
        # 如果文件不存在，则检查目录
        # 如果目录不存在，则将请求传递给Laravel的index.php
        try_files $uri $uri/ /index.php?$query_string;
    }

    # 如果请求的是index.html，直接提供Vue项目的入口文件
    location = /index.html {
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        expires 0;
    }

    # 处理静态资源，提高缓存效率
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        access_log off;
        expires max;
        add_header Cache-Control "public, max-age=31536000";
    }

    # 处理PHP请求
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php-fpm.sock; # 请根据您的PHP-FPM配置调整
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # 禁止访问.htaccess文件
    location ~ /\.ht {
        deny all;
    }

    # 禁止访问敏感文件
    location ~ /\.(git|env|env\.|env$) {
        deny all;
    }

    # 错误页面
    error_page 404 /index.php;
} 