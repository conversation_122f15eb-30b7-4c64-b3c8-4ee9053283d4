<template>
  <nav-bar :title="t('home')" :show-left-arrow="false" />
  <div class="content">
    <div class="demo-section">
      <h2 class="section-title">AdCenter 样式演示</h2>
      <div class="demo-cards">
        <div class="demo-card" @click="goToOriginal">
          <div class="card-icon original-icon">
            <van-icon name="shop-o" />
          </div>
          <div class="card-content">
            <h3>原始版本</h3>
            <p>传统的Facebook广告管理界面</p>
          </div>
          <van-icon name="arrow" class="card-arrow" />
        </div>

        <div class="demo-card tiktok-card" @click="goToTikTok">
          <div class="card-icon tiktok-icon">
            <van-icon name="video-o" />
          </div>
          <div class="card-content">
            <h3>TikTok风格</h3>
            <p>全新的沉浸式深色主题界面</p>
          </div>
          <van-icon name="arrow" class="card-arrow" />
        </div>
      </div>

      <h2 class="section-title">VIP页面演示</h2>
      <div class="demo-cards">
        <div class="demo-card" @click="goToOriginalVIP">
          <div class="card-icon original-icon">
            <van-icon name="diamond-o" />
          </div>
          <div class="card-content">
            <h3>原始VIP页面</h3>
            <p>传统的VIP特权展示界面</p>
          </div>
          <van-icon name="arrow" class="card-arrow" />
        </div>

        <div class="demo-card tiktok-card" @click="goToTikTokVIP">
          <div class="card-icon tiktok-icon">
            <van-icon name="diamond-o" />
          </div>
          <div class="card-content">
            <h3>TikTok风格VIP</h3>
            <p>炫酷的VIP特权展示界面</p>
          </div>
          <van-icon name="arrow" class="card-arrow" />
        </div>
      </div>

      <h2 class="section-title">钱包页面演示</h2>
      <div class="demo-cards">
        <div class="demo-card" @click="goToOriginalWallet">
          <div class="card-icon original-icon">
            <van-icon name="balance-o" />
          </div>
          <div class="card-content">
            <h3>原始钱包页面</h3>
            <p>传统的钱包资产管理界面</p>
          </div>
          <van-icon name="arrow" class="card-arrow" />
        </div>

        <div class="demo-card tiktok-card" @click="goToTikTokWallet">
          <div class="card-icon tiktok-icon">
            <van-icon name="balance-o" />
          </div>
          <div class="card-content">
            <h3>TikTok风格钱包</h3>
            <p>炫酷的资产展示和交易界面</p>
          </div>
          <van-icon name="arrow" class="card-arrow" />
        </div>
      </div>

      <h2 class="section-title">佣金页面演示</h2>
      <div class="demo-cards">
        <div class="demo-card" @click="goToOriginalCommission">
          <div class="card-icon original-icon">
            <van-icon name="balance-list-o" />
          </div>
          <div class="card-content">
            <h3>原始佣金页面</h3>
            <p>传统的佣金统计和团队管理界面</p>
          </div>
          <van-icon name="arrow" class="card-arrow" />
        </div>

        <div class="demo-card tiktok-card" @click="goToTikTokCommission">
          <div class="card-icon tiktok-icon">
            <van-icon name="balance-list-o" />
          </div>
          <div class="card-content">
            <h3>TikTok风格佣金</h3>
            <p>炫酷的佣金展示和团队数据界面</p>
          </div>
          <van-icon name="arrow" class="card-arrow" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import NavBar from '@/components/NavBar.vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { Icon as VanIcon } from 'vant'

const { t } = useI18n()
const router = useRouter()

const goToOriginal = () => {
  router.push('/adcenter')
}

const goToTikTok = () => {
  router.push('/adcenter-tiktok')
}

const goToOriginalVIP = () => {
  router.push('/adcenter/vip')
}

const goToTikTokVIP = () => {
  router.push('/adcenter/vip-tiktok')
}

const goToOriginalWallet = () => {
  router.push('/adcenter/wallet')
}

const goToTikTokWallet = () => {
  router.push('/adcenter/wallet-tiktok')
}

const goToOriginalCommission = () => {
  router.push('/adcenter/commission')
}

const goToTikTokCommission = () => {
  router.push('/adcenter/commission-tiktok')
}
</script>

<style scoped>
.content {
  padding: 0.2rem;
}

.demo-section {
  margin-top: 0.2rem;
}

.section-title {
  font-size: 0.2rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 0.2rem;
  text-align: center;
}

.demo-cards {
  display: flex;
  flex-direction: column;
  gap: 0.16rem;
}

.demo-card {
  display: flex;
  align-items: center;
  padding: 0.2rem;
  background: #ffffff;
  border-radius: 0.16rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.demo-card:active {
  transform: scale(0.98);
}

.demo-card:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.tiktok-card {
  background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
  color: #ffffff;
  border-color: rgba(255, 0, 80, 0.3);
}

.tiktok-card:hover {
  border-color: rgba(255, 0, 80, 0.6);
  box-shadow: 0 6px 20px rgba(255, 0, 80, 0.3);
}

.card-icon {
  width: 0.6rem;
  height: 0.6rem;
  border-radius: 0.12rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.16rem;
  font-size: 0.28rem;
}

.original-icon {
  background: linear-gradient(135deg, #1877f2, #42a5f5);
  color: #ffffff;
}

.tiktok-icon {
  background: linear-gradient(135deg, #ff0050, #ff4081);
  color: #ffffff;
}

.card-content {
  flex: 1;
}

.card-content h3 {
  font-size: 0.18rem;
  font-weight: 600;
  margin: 0 0 0.04rem 0;
}

.card-content p {
  font-size: 0.14rem;
  color: #666;
  margin: 0;
  line-height: 1.4;
}

.tiktok-card .card-content p {
  color: rgba(255, 255, 255, 0.8);
}

.card-arrow {
  font-size: 0.18rem;
  color: #999;
  margin-left: 0.12rem;
}

.tiktok-card .card-arrow {
  color: rgba(255, 255, 255, 0.8);
}

.banner {
  width: 100%;
  height: 1.5rem;
  background-color: #1989fa;
  border-radius: 0.1rem;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 0.2rem;
}

.banner h3 {
  font-size: 0.18rem;
  font-weight: bold;
}
</style>