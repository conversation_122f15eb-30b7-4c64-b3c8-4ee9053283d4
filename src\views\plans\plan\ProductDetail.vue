<template>
  <nav-bar :title="t('plans.productDetail')" :show-bell="false" />
  <div class="content" v-if="loading">
    <!-- 头部区域骨架屏 -->
    <div class="header skeleton-header">
      <div class="game-icon">
        <van-skeleton avatar avatar-size="1rem" />
      </div>

      <div class="skeleton-title">
        <van-skeleton title :row="0" title-width="60%" />
      </div>

      <div class="download-buttons">
        <div class="skeleton-download-button">
          <van-skeleton title :row="0" title-width="80%" />
        </div>
        <div class="skeleton-download-button">
          <van-skeleton title :row="0" title-width="80%" />
        </div>
      </div>
    </div>

    <div class="game-info-wrapper">
      <!-- 游戏横幅骨架屏 -->
      <div class="game-banner skeleton-banner">
        <van-skeleton title :row="0" title-width="100%" />
      </div>

      <!-- 游戏简介骨架屏 -->
      <div class="game-info">
        <div class="skeleton-subtitle">
          <van-skeleton title :row="0" title-width="30%" />
        </div>
        <van-skeleton title :row="3" />
      </div>

      <!-- 产品详情骨架屏 -->
      <div class="product-specs">
        <div class="skeleton-subtitle">
          <van-skeleton title :row="0" title-width="30%" />
        </div>

        <div class="spec-item" v-for="i in 8" :key="i">
          <van-skeleton title :row="0" title-width="20%" class="spec-label" />
          <van-skeleton title :row="0" title-width="30%" class="spec-value" />
        </div>
      </div>
    </div>
  </div>
  <div class="content" v-else-if="product">
    <div class="header">
      <div class="game-icon">
        <img :src="product.icon" :alt="t('plans.gameIcon')" />
      </div>

      <h1 class="game-title">{{ product.name }}</h1>

      <div class="download-buttons">
        <a :href="product.google_play_link" class="download-button" target="_blank" v-if="product.google_play_link">
          <img src="@/assets/image/plans/google-play.png" alt="Google Play" />
        </a>
        <a :href="product.app_store_link" class="download-button" target="_blank" v-if="product.app_store_link">
          <img src="@/assets/image/plans/apple-store.png" alt="App Store" />
        </a>
      </div>
    </div>

    <div class="game-info-wrapper">
      <div class="game-banner" v-if="product.screenshots">
        <img :src="product.screenshots" :alt="t('plans.gameScreenshot')" />
      </div>
      <div class="game-info">
        <h2>{{ t('plans.gameIntroduction') }}</h2>
        <p>{{ product.description }}</p>
      </div>

      <div class="product-specs">
        <h2>{{ t('plans.productDetail') }}</h2>
        <div class="spec-item" v-if="product.category">
          <span class="spec-label">{{ t('plans.category') }}</span>
          <span class="spec-value">{{ product.category }}</span>
        </div>
        <div class="spec-item" v-if="product.developer">
          <span class="spec-label">{{ t('plans.developer') }}</span>
          <span class="spec-value">{{ product.developer }}</span>
        </div>
        <div class="spec-item" v-if="product.languages">
          <span class="spec-label">{{ t('plans.supportedLanguages') }}</span>
          <span class="spec-value">{{ product.languages }}</span>
        </div>
        <div class="spec-item" v-if="product.ageRating">
          <span class="spec-label">{{ t('plans.ageRating') }}</span>
          <span class="spec-value">{{ product.ageRating }}</span>
        </div>
        <div class="spec-item" v-if="product.version">
          <span class="spec-label">{{ t('plans.version') }}</span>
          <span class="spec-value">{{ product.version }}</span>
        </div>
        <div class="spec-item" v-if="product.size">
          <span class="spec-label">{{ t('plans.size') }}</span>
          <span class="spec-value">{{ product.size }}</span>
        </div>
        <div class="spec-item" v-if="product.installs">
          <span class="spec-label">{{ t('plans.installCount') }}</span>
          <span class="spec-value">{{ product.installs }}</span>
        </div>
        <div class="spec-item" v-if="product.rating">
          <span class="spec-label">{{ t('plans.rating') }}</span>
          <span class="spec-value">{{ product.rating }}</span>
        </div>
        <div class="spec-item" v-if="product.lastUpdated">
          <span class="spec-label">{{ t('plans.lastUpdated') }}</span>
          <span class="spec-value">{{ product.lastUpdated }}</span>
        </div>
      </div>
    </div>
  </div>
  <div class="content" v-else>
    <van-empty :description="t('plans.productNotFound')" />
    <van-button type="primary" block @click="$router.back()">{{ t('plans.back') }}</van-button>
  </div>
</template>

<script setup>
import { useRoute, useRouter } from 'vue-router'
import { Loading as VanLoading, Empty as VanEmpty, Button as VanButton, Skeleton as VanSkeleton } from 'vant'
import NavBar from '@/components/NavBar.vue'
import { computed, ref, onMounted } from 'vue'
import { useTitle } from '@/utils/useTitle'
import { planApi } from '@/api'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const productId = computed(() => route.params.productId)

// 产品数据
const product = ref(null)
const loading = ref(true)

// 获取产品详情
const fetchProductDetail = async () => {
  loading.value = true
  try {
    const res = await planApi.getProductDetail(productId.value)
    if (res && res.data) {
      product.value = res.data
    }
  } catch (error) {
    console.error(t('plans.fetchProductDetailFailed'), error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchProductDetail()
})

// 设置页面标题
useTitle(() => t('plans.productDetail'))
</script>

<style scoped>
.content>.van-loading {
  margin: 2rem auto;
}

.header {
  background-image: url('@/assets/image/plans/product-banner.png');
  background-size: cover;
  background-position: center;
  padding: 0.4rem 0;
  margin-bottom: 0.2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 骨架屏样式 */
.skeleton-header {
  background-image: none;
  background-color: #f5f5f5;
}

.skeleton-title {
  width: 60%;
  margin: 0.2rem 0;
}

.download-buttons {
  display: flex;
  justify-content: center;
  gap: 0.2rem;
}

.download-button img {
  height: 0.48rem;
  border-radius: 0.08rem;
}

.skeleton-download-button {
  width: 1.2rem;
  height: 0.48rem;
  background-color: #f2f3f5;
  border-radius: 0.08rem;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  box-shadow: 0 0.02rem 0.04rem rgba(0, 0, 0, 0.1);
}

.skeleton-download-button :deep(.van-skeleton__title) {
  height: 0.24rem;
}

.skeleton-banner {
  height: 1.8rem;
  display: flex;
  align-items: center;
}

.skeleton-banner :deep(.van-skeleton__title) {
  height: 100%;
}

.skeleton-subtitle {
  margin-bottom: 0.16rem;
}

:deep(.van-skeleton__title) {
  height: 0.16rem;
}

:deep(.van-skeleton__row) {
  height: 0.14rem;
  margin-top: 0.08rem;
}

:deep(.van-skeleton__avatar) {
  background-color: #f2f3f5;
}

.game-info-wrapper {
  padding: 0.16rem;
}

.game-icon {
  width: 1rem;
  height: 1rem;
  border-radius: 0.2rem;
  overflow: hidden;
  margin: auto;
  box-shadow: 0 0.04rem 0.08rem rgba(0, 0, 0, 0.1);
}

.game-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.game-title {
  text-align: center;
  font-size: 0.24rem;
  font-weight: bold;
  margin: 0.2rem 0;
  text-shadow: 0 0.02rem 0.04rem rgba(0, 0, 0, 0.3);
}

.download-buttons {
  display: flex;
  justify-content: center;
  gap: 0.2rem;
}

.download-button img {
  height: 0.48rem;
  border-radius: 0.08rem;
}

.game-banner {
  height: 1.8rem;
  border-radius: 0.16rem;
  overflow: hidden;
}

.game-banner img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.game-info,
.product-specs {
  margin-top: 0.3rem;
}

.game-info h2,
.product-specs h2 {
  font-size: 0.18rem;
  font-weight: bold;
  margin-bottom: 0.16rem;
}

.game-info p {
  margin-bottom: 0.12rem;
  line-height: 1.5;
  font-size: 0.14rem;
  color: #333;
}

.spec-item {
  display: flex;
  padding: 0.16rem 0;
  border-bottom: 1px solid #f0f0f0;
}

.spec-label {
  flex: 1;
  color: #666;
}

.spec-value {
  flex: 1;
  text-align: right;
  font-weight: 500;
}

.van-empty {
  margin: 2rem 0;
}

.van-button {
  width: 80%;
  margin: 0 auto;
}
</style>