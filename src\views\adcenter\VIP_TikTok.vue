<template>
  <!-- TikTok风格VIP页面 -->
  <div class="tiktok-vip-container">
    <!-- 顶部导航 -->
    <div class="tiktok-header">
      <div class="header-left">
        <van-icon name="arrow-left" class="back-icon" @click="goBack" />
      </div>
      <div class="header-center">
        <span class="page-title">{{ t('vip.title') }}</span>
      </div>
      <div class="header-right">
        <van-icon name="ellipsis" class="menu-icon" />
      </div>
    </div>

    <div class="tiktok-content">
      <!-- 加载状态 -->
      <template v-if="pageLoading">
        <div class="tiktok-skeleton-container">
          <!-- VIP总览骨架屏 -->
          <div class="tiktok-vip-overview skeleton">
            <van-skeleton avatar avatar-size="0.8rem" title :row="2" />
          </div>
          
          <!-- VIP列表骨架屏 -->
          <div class="tiktok-vip-list">
            <div class="tiktok-vip-card skeleton" v-for="i in 3" :key="i">
              <van-skeleton avatar avatar-size="0.6rem" title :row="3" />
            </div>
          </div>
        </div>
      </template>

      <template v-else>
        <!-- TikTok风格VIP总览卡片 -->
        <div class="tiktok-vip-overview" v-if="userVip">
          <div class="vip-background">
            <div class="vip-glow"></div>
          </div>
          <div class="vip-content">
            <div class="vip-avatar-section">
              <div class="vip-avatar-container">
                <img :src="getVipIcon(userVip.level)" alt="VIP图标" class="vip-avatar" />
                <div class="avatar-crown">
                  <van-icon name="diamond-o" class="crown-icon" />
                </div>
                <div class="avatar-ring"></div>
              </div>
            </div>
            <div class="vip-info-section">
              <h2 class="vip-level">VIP {{ userVip.level }}</h2>
              <p class="vip-status">{{ t('vip.currentVip') }}</p>
              <div class="vip-privileges">
                <div class="privilege-item">
                  <van-icon name="balance-o" class="privilege-icon" />
                  <span>{{ userVip.commission }}</span>
                </div>
                <div class="privilege-item">
                  <van-icon name="friends-o" class="privilege-icon" />
                  <span>{{ userVip.downLineLimit }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- VIP特权入口按钮 -->
        <div class="tiktok-vip-entry" v-if="!userVip">
          <div class="entry-card" @click="showAllVips">
            <div class="entry-background"></div>
            <div class="entry-content">
              <van-icon name="diamond-o" class="entry-icon" />
              <h3 class="entry-title">{{ t('vip.openVip') }}</h3>
              <p class="entry-desc">解锁专属特权，享受更多收益</p>
            </div>
            <div class="entry-glow"></div>
          </div>
        </div>

        <!-- TikTok风格VIP列表 -->
        <div class="tiktok-vip-list">
          <div 
            class="tiktok-vip-card" 
            v-for="vip in vipList" 
            :key="vip.id" 
            :class="{'current-vip': vip.isCurrent, 'premium-vip': vip.level >= 3}"
          >
            <div class="vip-card-background">
              <div class="card-glow" :class="`glow-level-${vip.level}`"></div>
            </div>
            
            <div class="vip-card-content">
              <!-- 卡片头部 -->
              <div class="vip-card-header">
                <div class="vip-icon-wrapper">
                  <img :src="getVipIcon(vip.level)" alt="VIP图标" class="vip-icon" />
                  <div class="icon-shine"></div>
                </div>
                <div class="vip-title-section">
                  <div class="vip-title-row">
                    <span class="vip-title">VIP {{ vip.level }}</span>
                    <van-tag type="warning" v-if="vip.tag" class="vip-tag">{{ vip.tag }}</van-tag>
                    <van-tag type="success" v-if="vip.isCurrent" class="current-tag">{{ t('vip.currentLevel') }}</van-tag>
                  </div>
                  <div class="vip-price-section">
                    <span class="price-symbol">$</span>
                    <span class="price-value">{{ vip.price }}</span>
                  </div>
                </div>
              </div>

              <!-- 特权详情 -->
              <div class="vip-details">
                <div class="detail-grid">
                  <div class="detail-item">
                    <div class="detail-icon-wrapper">
                      <van-icon name="balance-o" class="detail-icon" />
                    </div>
                    <div class="detail-info">
                      <div class="detail-label">{{ t('vip.commissionRatio') }}</div>
                      <div class="detail-value">{{ vip.commission }}</div>
                    </div>
                  </div>
                  <div class="detail-item">
                    <div class="detail-icon-wrapper">
                      <van-icon name="friends-o" class="detail-icon" />
                    </div>
                    <div class="detail-info">
                      <div class="detail-label">{{ t('vip.downlineLimit') }}</div>
                      <div class="detail-value">{{ vip.downLineLimit }}</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 特权列表 -->
              <div class="vip-features">
                <div class="features-title">专属特权</div>
                <div class="features-list">
                  <div class="feature-item" v-for="(feature, index) in vip.features" :key="index">
                    <van-icon name="success" class="feature-check" />
                    <span class="feature-text">{{ feature }}</span>
                  </div>
                </div>
              </div>

              <!-- 激活按钮 -->
              <div class="vip-action">
                <div 
                  class="tiktok-activate-btn" 
                  :class="{
                    'disabled': !canUpgrade(vip.level),
                    'current': vip.isCurrent,
                    'premium': vip.level >= 3
                  }"
                  @click="showVipConfirm(vip)"
                >
                  <div class="btn-background"></div>
                  <div class="btn-content">
                    <span class="btn-text">
                      {{ vip.isCurrent ? t('vip.alreadyActivated') : (!vip.canUpgrade ? t('vip.noNeedToActivate') : t('vip.activateNow')) }}
                    </span>
                  </div>
                  <div class="btn-glow"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>

  <!-- TikTok风格确认弹窗 -->
  <van-dialog
    v-model:show="showConfirmDialog"
    :title="t('vip.confirmUpgrade')"
    show-cancel-button
    :confirm-button-text="t('vip.confirmUpgrade')"
    :cancel-button-text="t('vip.cancel')"
    @confirm="handleConfirmUpgrade"
    :loading="loading"
    class="tiktok-dialog"
  >
    <div class="tiktok-confirm-content">
      <div class="confirm-icon">
        <van-icon name="diamond-o" class="diamond-icon" />
      </div>
      <p class="confirm-text">{{ t('vip.upgradeToVip', { level: selectedVip?.level }) }}</p>
      <p class="confirm-price">{{ t('vip.activationFee', { price: selectedVip?.price }) }}</p>
    </div>
  </van-dialog>

  <!-- 交易密码验证组件 -->
  <transaction-password-verify
    v-model:show="showPasswordVerify"
    :title="t('vip.paymentPasswordTitle')"
    :tip-text="t('vip.paymentPasswordTip')"
    @confirm="confirmVipUpgrade"
    @cancel="cancelPasswordVerify"
    @verification-error="handleVerificationError"
  />
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { Dialog, Button as VanButton, Icon as VanIcon, Tag as VanTag, Loading as VanLoading, showToast, showFailToast, showSuccessToast, Skeleton as VanSkeleton } from 'vant'
import { vipApi } from '@/api'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import TransactionPasswordVerify from '@/components/TransactionPasswordVerify.vue'
import { getUserInfo } from '@/utils/useUserInfo'

// 国际化和路由
const { t } = useI18n()
const router = useRouter()

// 返回上一页
const goBack = () => {
  router.back()
}

// 选中的VIP信息
const selectedVip = ref(null)

// 确认弹窗显示状态
const showConfirmDialog = ref(false)

// 交易密码验证弹窗显示状态
const showPasswordVerify = ref(false)

// 加载状态
const loading = ref(false)

// VIP列表数据
const vipList = ref([])

// 页面加载状态
const pageLoading = ref(false)

// 获取VIP列表
const fetchVipList = async () => {
  try {
    pageLoading.value = true
    const response = await vipApi.getVipList()
    const data = response.data || []
    vipList.value = data.map(item => ({
      id: item.id,
      level: item.level,
      name: item.name,
      tag: item.name,
      price: item.price,
      commission: formatCommissionRatio(item.commission_ratios),
      downLineLimit: item.subordinate_limit === 0 ? t('vip.unlimited') : String(item.subordinate_limit),
      features: formatFeatures(item),
      icon: item.icon,
      isCurrent: item.is_current,
      canUpgrade: item.can_upgrade
    }))
  } catch (error) {
    console.error('获取VIP列表失败:', error)
    showFailToast(error.message || t('vip.loadFailed'))
  } finally {
    pageLoading.value = false
  }
}

// 格式化分佣比例
const formatCommissionRatio = (ratios) => {
  if (!ratios) return '0%'
  
  // 如果有level1的比例，显示level1的比例
  if (ratios.level1) {
    return (ratios.level1 * 100).toFixed(1) + '%'
  }
  
  return '0%'
}

// 格式化VIP特权描述
const formatFeatures = (vip) => {
  let features = []
  
  // 如果description是数组，直接使用
  if (Array.isArray(vip.description)) {
    features = [...vip.description]
  } 
  // 如果是字符串，添加到数组中
  else if (vip.description) {
    features.push(vip.description)
  }
  
  // 如果有特权列表，添加到features中
  if (Array.isArray(vip.privileges)) {
    features = [...features, ...vip.privileges]
  }
  
  return features
}

// 用户当前VIP信息
const userVip = computed(() => {
  if (vipList.value.length === 0) return null
  return vipList.value.find(vip => vip.isCurrent) || null
})

// 获取VIP图标
const getVipIcon = (level) => {
  const vip = vipList.value.find(v => v.level === level)
  if (vip && vip.icon) {
    return vip.icon
  }
  // 使用默认图标
  return `/src/assets/image/adcenter/vip/vip${level}.jpg`
}

// 显示所有VIP信息
const showAllVips = () => {
  // 如果需要在点击按钮时处理特殊逻辑，可以在这里实现
}

// 显示VIP确认弹窗
const showVipConfirm = (vip) => {
  // 检查是否可以升级
  if (!vip.canUpgrade) {
    Dialog.alert({
      title: t('tips'),
      message: t('vip.alreadyVip', { level: userVip.value?.level || 0 }),
    })
    return
  }
  
  selectedVip.value = vip
  showConfirmDialog.value = true
}

// 处理确认升级按钮点击
const handleConfirmUpgrade = () => {
  showConfirmDialog.value = false
  // 显示交易密码验证弹窗
  showPasswordVerify.value = true
}

// 取消密码验证
const cancelPasswordVerify = () => {
  showToast(t('vip.upgradeCanceled'))
}

// 处理验证错误
const handleVerificationError = ({ message }) => {
  showFailToast(message)
}

// 确认VIP升级
const confirmVipUpgrade = async (password) => {
  if (!selectedVip.value) return
  
  try {
    loading.value = true
    
    // 调用VIP升级接口
    await vipApi.upgradeVip({ 
      vip_id: selectedVip.value.id,
      payment_password: password
    })
    
    // 关闭密码验证弹窗
    showPasswordVerify.value = false
    
    // 升级成功后更新用户信息
    await getUserInfo(true) // 强制更新用户信息
    
    // 重新获取VIP列表
    await fetchVipList()
    
    // 显示成功提示
    showSuccessToast(t('vip.upgradeSuccess'))
    
  } catch (error) {
    console.error('VIP升级失败:', error)
    
    // 显示错误提示
    showFailToast(error.message || t('vip.upgradeFailed'))
    
  } finally {
    loading.value = false
  }
}

// 判断是否可以升级到指定VIP等级
const canUpgrade = (level) => {
  const vip = vipList.value.find(v => v.level === level)
  return vip ? vip.canUpgrade : false
}

// 页面加载时获取VIP列表
onMounted(() => {
  fetchVipList()
})
</script>

<style scoped>
/* TikTok风格VIP页面全局样式 */
.tiktok-vip-container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(180deg, #000000 0%, #1a1a1a 30%, #2d1b69 70%, #000000 100%);
  color: #ffffff;
  overflow-x: hidden;
}

/* TikTok风格顶部导航 */
.tiktok-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 0.6rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 0.16rem;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 1000;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-left,
.header-right {
  width: 0.4rem;
  display: flex;
  justify-content: center;
}

.header-center {
  flex: 1;
  text-align: center;
}

.back-icon,
.menu-icon {
  font-size: 0.24rem;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-icon:active,
.menu-icon:active {
  transform: scale(0.9);
  color: #ff0050;
}

.page-title {
  font-size: 0.18rem;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.tiktok-content {
  padding: 0.8rem 0.16rem 0.4rem;
}

/* 骨架屏样式 */
.tiktok-skeleton-container {
  animation: fadeIn 0.3s ease;
}

.tiktok-vip-overview.skeleton {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0.2rem;
  padding: 0.24rem;
  margin-bottom: 0.24rem;
  backdrop-filter: blur(10px);
}

.tiktok-vip-card.skeleton {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 0.2rem;
  padding: 0.2rem;
  margin-bottom: 0.16rem;
  backdrop-filter: blur(10px);
}

.tiktok-skeleton-container :deep(.van-skeleton) {
  background: rgba(255, 255, 255, 0.1);
}

.tiktok-skeleton-container :deep(.van-skeleton__avatar) {
  background: rgba(255, 255, 255, 0.2);
}

.tiktok-skeleton-container :deep(.van-skeleton__title) {
  background: rgba(255, 255, 255, 0.15);
}

.tiktok-skeleton-container :deep(.van-skeleton__row) {
  background: rgba(255, 255, 255, 0.1);
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* TikTok风格VIP总览卡片 */
.tiktok-vip-overview {
  position: relative;
  margin-bottom: 0.24rem;
  border-radius: 0.24rem;
  overflow: hidden;
  background: linear-gradient(135deg, #ff0050 0%, #ff4081 50%, #9c27b0 100%);
  box-shadow: 0 12px 40px rgba(255, 0, 80, 0.4);
}

.vip-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 0, 80, 0.9) 0%, rgba(156, 39, 176, 0.9) 100%);
}

.vip-glow {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
  animation: vipGlow 4s ease-in-out infinite;
}

@keyframes vipGlow {
  0%, 100% { transform: rotate(0deg) scale(1); opacity: 0.3; }
  50% { transform: rotate(180deg) scale(1.1); opacity: 0.6; }
}

.vip-content {
  position: relative;
  z-index: 2;
  padding: 0.32rem;
  display: flex;
  align-items: center;
  gap: 0.2rem;
}

.vip-avatar-section {
  position: relative;
}

.vip-avatar-container {
  position: relative;
  width: 0.8rem;
  height: 0.8rem;
}

.vip-avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 3px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

.avatar-crown {
  position: absolute;
  top: -0.08rem;
  right: -0.08rem;
  width: 0.24rem;
  height: 0.24rem;
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.6);
}

.crown-icon {
  font-size: 0.12rem;
  color: #000;
}

.avatar-ring {
  position: absolute;
  top: -6px;
  left: -6px;
  right: -6px;
  bottom: -6px;
  border: 2px solid transparent;
  border-radius: 50%;
  background: linear-gradient(45deg, #ffd700, #ff0050, #9c27b0, #ffd700);
  background-size: 400% 400%;
  animation: avatarRing 3s ease-in-out infinite;
  z-index: -1;
}

@keyframes avatarRing {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.vip-info-section {
  flex: 1;
}

.vip-level {
  font-size: 0.28rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 0.08rem 0;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
}

.vip-status {
  font-size: 0.14rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0 0 0.16rem 0;
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
}

.vip-privileges {
  display: flex;
  gap: 0.16rem;
}

.privilege-item {
  display: flex;
  align-items: center;
  gap: 0.06rem;
  background: rgba(0, 0, 0, 0.3);
  padding: 0.06rem 0.12rem;
  border-radius: 0.12rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.privilege-icon {
  font-size: 0.12rem;
  color: #ffd700;
}

.privilege-item span {
  font-size: 0.12rem;
  font-weight: 600;
  color: #ffffff;
}

/* TikTok风格VIP入口卡片 */
.tiktok-vip-entry {
  margin-bottom: 0.24rem;
}

.entry-card {
  position: relative;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-radius: 0.2rem;
  padding: 0.32rem;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  text-align: center;
}

.entry-card:active {
  transform: scale(0.98);
}

.entry-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 0, 80, 0.1), rgba(0, 255, 136, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.entry-card:active .entry-background {
  opacity: 1;
}

.entry-content {
  position: relative;
  z-index: 2;
}

.entry-icon {
  font-size: 0.48rem;
  color: #ffd700;
  margin-bottom: 0.16rem;
  text-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
  animation: entryIconPulse 2s ease-in-out infinite;
}

@keyframes entryIconPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.entry-title {
  font-size: 0.2rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 0.08rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.entry-desc {
  font-size: 0.14rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.entry-glow {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, rgba(255, 215, 0, 0.3), rgba(255, 0, 80, 0.3));
  border-radius: 0.22rem;
  opacity: 0;
  z-index: -1;
  transition: opacity 0.3s ease;
}

.entry-card:hover .entry-glow {
  opacity: 1;
  animation: entryGlow 2s ease-in-out infinite;
}

@keyframes entryGlow {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.6; }
}

/* TikTok风格VIP列表 */
.tiktok-vip-list {
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
}

.tiktok-vip-card {
  position: relative;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-radius: 0.2rem;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  transition: all 0.3s ease;
  animation: cardSlideIn 0.6s ease forwards;
}

.tiktok-vip-card:nth-child(1) { animation-delay: 0.1s; }
.tiktok-vip-card:nth-child(2) { animation-delay: 0.2s; }
.tiktok-vip-card:nth-child(3) { animation-delay: 0.3s; }

@keyframes cardSlideIn {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}

.tiktok-vip-card.current-vip {
  border-color: rgba(255, 215, 0, 0.6);
  box-shadow: 0 8px 32px rgba(255, 215, 0, 0.3);
  transform: scale(1.02);
}

.tiktok-vip-card.premium-vip {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.2) 0%, rgba(255, 193, 7, 0.1) 100%);
  border-color: rgba(255, 215, 0, 0.4);
}

.vip-card-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: inherit;
  opacity: 0.8;
}

.card-glow {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 0.22rem;
  opacity: 0;
  z-index: -1;
  transition: opacity 0.3s ease;
}

.glow-level-1 {
  background: linear-gradient(45deg, rgba(33, 150, 243, 0.3), rgba(3, 169, 244, 0.3));
}

.glow-level-2 {
  background: linear-gradient(45deg, rgba(76, 175, 80, 0.3), rgba(139, 195, 74, 0.3));
}

.glow-level-3 {
  background: linear-gradient(45deg, rgba(255, 193, 7, 0.3), rgba(255, 235, 59, 0.3));
}

.glow-level-4 {
  background: linear-gradient(45deg, rgba(255, 87, 34, 0.3), rgba(255, 152, 0, 0.3));
}

.glow-level-5 {
  background: linear-gradient(45deg, rgba(156, 39, 176, 0.3), rgba(233, 30, 99, 0.3));
}

.tiktok-vip-card:hover .card-glow {
  opacity: 1;
  animation: cardGlow 2s ease-in-out infinite;
}

@keyframes cardGlow {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.6; }
}

.vip-card-content {
  position: relative;
  z-index: 2;
  padding: 0.2rem;
}

/* VIP卡片头部 */
.vip-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.2rem;
  padding-bottom: 0.16rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.vip-icon-wrapper {
  position: relative;
  width: 0.6rem;
  height: 0.6rem;
  margin-right: 0.16rem;
}

.vip-icon {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.6);
  box-shadow: 0 0 16px rgba(255, 255, 255, 0.4);
}

.icon-shine {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 50%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: iconShine 3s ease-in-out infinite;
}

@keyframes iconShine {
  0%, 100% { transform: rotate(0deg); opacity: 0; }
  50% { transform: rotate(180deg); opacity: 1; }
}

.vip-title-section {
  flex: 1;
}

.vip-title-row {
  display: flex;
  align-items: center;
  margin-bottom: 0.08rem;
}

.vip-title {
  font-size: 0.2rem;
  font-weight: 700;
  color: #ffffff;
  margin-right: 0.12rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.vip-tag,
.current-tag {
  font-size: 0.1rem;
  padding: 0.02rem 0.08rem;
  border-radius: 0.08rem;
  margin-left: 0.06rem;
}

.vip-tag {
  background: linear-gradient(45deg, #ff9800, #ffc107);
  color: #000;
}

.current-tag {
  background: linear-gradient(45deg, #4caf50, #8bc34a);
  color: #fff;
}

.vip-price-section {
  display: flex;
  align-items: baseline;
}

.price-symbol {
  font-size: 0.14rem;
  color: #ff0050;
  font-weight: 600;
  margin-right: 0.02rem;
}

.price-value {
  font-size: 0.24rem;
  font-weight: 700;
  color: #ff0050;
  text-shadow: 0 0 10px rgba(255, 0, 80, 0.5);
}

/* VIP特权详情 */
.vip-details {
  margin-bottom: 0.2rem;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.12rem;
}

.detail-item {
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.3);
  padding: 0.12rem;
  border-radius: 0.12rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.detail-icon-wrapper {
  width: 0.32rem;
  height: 0.32rem;
  border-radius: 0.08rem;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.1rem;
}

.detail-icon {
  font-size: 0.16rem;
  color: #00ff88;
}

.detail-info {
  flex: 1;
}

.detail-label {
  font-size: 0.1rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 0.02rem;
}

.detail-value {
  font-size: 0.12rem;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* VIP特权列表 */
.vip-features {
  margin-bottom: 0.2rem;
}

.features-title {
  font-size: 0.14rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 0.12rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.features-list {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 0.12rem;
  padding: 0.12rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.feature-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0.08rem;
  font-size: 0.12rem;
  line-height: 1.4;
}

.feature-item:last-child {
  margin-bottom: 0;
}

.feature-check {
  font-size: 0.12rem;
  color: #00ff88;
  margin-right: 0.08rem;
  margin-top: 0.02rem;
  flex-shrink: 0;
}

.feature-text {
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* VIP激活按钮 */
.vip-action {
  margin-top: 0.16rem;
}

.tiktok-activate-btn {
  position: relative;
  width: 100%;
  height: 0.48rem;
  border-radius: 0.24rem;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tiktok-activate-btn:not(.disabled):not(.current):active {
  transform: scale(0.98);
}

.btn-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #ff0050 0%, #ff4081 100%);
  transition: all 0.3s ease;
}

.tiktok-activate-btn.premium .btn-background {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
}

.tiktok-activate-btn.current .btn-background {
  background: linear-gradient(135deg, #4caf50 0%, #8bc34a 100%);
}

.tiktok-activate-btn.disabled .btn-background {
  background: linear-gradient(135deg, #666 0%, #888 100%);
}

.btn-content {
  position: relative;
  z-index: 2;
}

.btn-text {
  font-size: 0.14rem;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.tiktok-activate-btn.premium .btn-text {
  color: #000;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
}

.btn-glow {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, rgba(255, 0, 80, 0.6), rgba(255, 64, 129, 0.6));
  border-radius: 0.26rem;
  opacity: 0;
  z-index: -1;
  transition: opacity 0.3s ease;
}

.tiktok-activate-btn.premium .btn-glow {
  background: linear-gradient(45deg, rgba(255, 215, 0, 0.6), rgba(255, 237, 78, 0.6));
}

.tiktok-activate-btn:not(.disabled):not(.current):hover .btn-glow {
  opacity: 1;
  animation: btnGlow 2s ease-in-out infinite;
}

@keyframes btnGlow {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

/* TikTok风格弹窗样式 */
.tiktok-dialog :deep(.van-dialog) {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(26, 26, 26, 0.9) 100%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
}

.tiktok-dialog :deep(.van-dialog__header) {
  color: #ffffff;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.tiktok-dialog :deep(.van-dialog__content) {
  color: #ffffff;
}

.tiktok-dialog :deep(.van-dialog__confirm) {
  color: #ff0050;
  font-weight: 600;
}

.tiktok-dialog :deep(.van-dialog__cancel) {
  color: rgba(255, 255, 255, 0.8);
}

.tiktok-confirm-content {
  text-align: center;
  padding: 0.2rem 0;
}

.confirm-icon {
  margin-bottom: 0.16rem;
}

.diamond-icon {
  font-size: 0.48rem;
  color: #ffd700;
  text-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
  animation: confirmIconPulse 2s ease-in-out infinite;
}

@keyframes confirmIconPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.confirm-text {
  font-size: 0.16rem;
  color: #ffffff;
  margin: 0.1rem 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.confirm-price {
  font-size: 0.18rem;
  font-weight: 700;
  color: #ff0050;
  margin: 0.1rem 0;
  text-shadow: 0 0 10px rgba(255, 0, 80, 0.5);
}
</style>
