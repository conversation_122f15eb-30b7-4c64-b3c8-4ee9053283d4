<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { applyTopPadding } from '@/utils/paddingAdjuster'
import { useSwipeBack } from '@/utils/swipeBack'

const router = useRouter()
const { initSwipeBack, destroySwipeBack } = useSwipeBack()

onMounted(() => {
  // 应用顶部内边距
  applyTopPadding()
  // 初始化滑动返回功能
  initSwipeBack()
})

onUnmounted(() => {
  // 移除滑动返回事件监听
  destroySwipeBack()
})

// 监听路由变化，确保滑动返回功能在路由变化时正确更新
watch(() => router.currentRoute.value, () => {
  // 重新初始化滑动返回功能
  destroySwipeBack()
  initSwipeBack()
}, { deep: true })

// 过渡动画名称
const getTransitionName = (to, from) => {
  // 如果没有from，说明是首次加载
  if (!from) return ''
  
  // 检查是否为子路由返回父路由
  if (from.path.startsWith(to.path + '/') && from.path !== to.path) {
    return 'slide-right'
  }
  
  // 检查是否为后退操作
  if (router.options.isBack) {
    router.options.isBack = false
    return 'slide-right'
  }
  
  // 默认为前进动画
  return 'slide-left'
}
</script>

<template>
  <div class="app-container">
    <router-view v-slot="{ Component, route }">
      <transition :name="getTransitionName(route, router.currentRoute.value)" mode="out-in">
        <div class="view-wrapper" :key="route.path">
          <keep-alive v-if="route.meta.keepAlive">
            <component :is="Component" />
          </keep-alive>
          <component v-else :is="Component" />
        </div>
      </transition>
    </router-view>
  </div>
</template>

<style scoped>
.app-container {
  position: relative;
  width: 100%;
  height: 100vh;
}

.view-wrapper {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}

/* 从右到左的过渡效果（前进） */
.slide-left-enter-active,
.slide-left-leave-active,
.slide-right-enter-active,
.slide-right-leave-active {
  transition: all 0.2s ease;
  position: absolute;
  width: 100%;
  top: 0;
}

/* 进入开始状态 - 从右侧进入 */
.slide-left-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

/* 离开结束状态 - 向左侧离开 */
.slide-left-leave-to {
  opacity: 0;
  transform: translateX(-30%);
  z-index: -1;
}

/* 进入开始状态 - 从左侧进入 */
.slide-right-enter-from {
  opacity: 0;
  transform: translateX(-100%);
}

/* 离开结束状态 - 向右侧离开 */
.slide-right-leave-to {
  opacity: 0;
  transform: translateX(30%);
  z-index: -1;
}
</style>
