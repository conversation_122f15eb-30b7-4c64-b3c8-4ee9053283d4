<template>
  <nav-bar :title="t('password.passwordTitle')" />
  <div class="content">
    <div class="step-tips">{{ currentStep === 1 ? t('password.passwordFirstStepTips') :
      t('password.passwordSecondStepTips') }}</div>

    <van-password-input :value="firstPassword" :focused="showKeyboard" :length="6" @focus="showKeyboard = true"
      v-show="currentStep === 1" />
    <van-password-input :value="confirmPassword" :focused="showKeyboard" :length="6" @focus="showKeyboard = true"
      v-show="currentStep === 2" />

    <div class="next-btn" v-if="currentStep === 1">
      <van-button type="primary" :disabled="firstPassword.length !== 6" block @click="goToNextStep">
        {{ t('password.passwordNextStep') }}
      </van-button>
    </div>

    <div class="submit-btn" v-if="currentStep === 2">
      <van-button type="primary" :disabled="confirmPassword.length !== 6" block @click="submitPassword"
        :loading="isSubmitting">
        {{ t('password.passwordSubmit') }}
      </van-button>
    </div>

    <van-number-keyboard :show="showKeyboard" @blur="showKeyboard = false" @input="onInput" @delete="onDelete" />
  </div>
</template>

<script setup>
import NavBar from '@/components/NavBar.vue'
import { ref } from 'vue'
import { showToast, PasswordInput as VanPasswordInput, NumberKeyboard as VanNumberKeyboard, showDialog } from 'vant'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { walletApi } from '@/api'
import { useUserStore } from '@/stores/user'

const { t } = useI18n()
const router = useRouter()
const userStore = useUserStore()

// 状态管理
const currentStep = ref(1) // 当前步骤：1-设置密码，2-确认密码
const firstPassword = ref('') // 第一次输入的密码
const confirmPassword = ref('') // 第二次输入的密码
const showKeyboard = ref(false) // 是否显示键盘
const isSubmitting = ref(false) // 是否正在提交

// 处理键盘输入
const onInput = (key) => {
  if (currentStep.value === 1) {
    if (firstPassword.value.length < 6) {
      firstPassword.value += key
    }
  } else {
    if (confirmPassword.value.length < 6) {
      confirmPassword.value += key
    }
  }
}

// 处理删除键
const onDelete = () => {
  if (currentStep.value === 1) {
    firstPassword.value = firstPassword.value.slice(0, -1)
  } else {
    confirmPassword.value = confirmPassword.value.slice(0, -1)
  }
}

// 进入下一步
const goToNextStep = () => {
  if (firstPassword.value.length === 6) {
    currentStep.value = 2
    showKeyboard.value = true
  }
}

// 提交密码
const submitPassword = async () => {
  if (confirmPassword.value.length === 6) {
    if (firstPassword.value === confirmPassword.value) {
      try {
        isSubmitting.value = true
        const res = await walletApi.setTransactionPassword({
          payPassword: firstPassword.value,
          confirmPayPassword: confirmPassword.value
        })

        if (res.code === 200) {
          showDialog({
            message: t('password.passwordSuccessMsg'),
          }).then(() => {
            // 更新用户信息中的密码设置状态
            if (userStore.userInfo) {
              const updatedUserInfo = {
                ...userStore.userInfo,
                isSetPwd: 1
              }
              userStore.setUserInfo(updatedUserInfo)
            }
            router.push('/adcenter/wallet')
          });

        } else {
          showToast(res.message || t('password.passwordErrorMsg'))
        }
      } catch (error) {
        console.error('设置交易密码失败:', error)
        showToast(t('password.passwordSetFailed'))
      } finally {
        isSubmitting.value = false
      }
    } else {
      showToast(t('password.passwordErrorMsg'))
      // 重置状态
      currentStep.value = 1
      firstPassword.value = ''
      confirmPassword.value = ''
      showKeyboard.value = true
    }
  }
}
</script>

<style scoped>
.content {
  padding: 0 0.16rem;
}

.step-tips {
  margin: 0.5rem 0;
  font-size: 0.16rem;
  text-align: center;
  color: #333;
}

.next-btn,
.submit-btn {
  margin-top: 0.6rem;
}
</style>