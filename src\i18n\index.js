import { createI18n } from 'vue-i18n'
import zh from './locales/zh'
import en from './locales/en'
import zhTW from './locales/zh-TW'

// 语言映射表，用于将localStorage中的语言标识映射到i18n语言文件
export const LOCALE_MAPPING = {
  'zh_CN': 'zh',
  'zh': 'zh',
  'en_US': 'en',
  'en': 'en',
  'zh_TW': 'zh-TW',
  'zh-TW': 'zh-TW',
  'zh-CN': 'zh',
  'en-US': 'en',
  'en-GB': 'en',
  'en-AU': 'en',
  'en-CA': 'en',
  'en-NZ': 'en',
  'en-ZA': 'en',
  'zh-Hans': 'zh',
  'zh-Hant': 'zh-TW',
  // 可以在此处添加更多映射
}

// 获取浏览器首选语言
function getDefaultLocale() {
  // 首先尝试从本地存储获取
  const savedLocale = localStorage.getItem('locale')
  if (savedLocale) {
    // 使用映射表转换语言标识
    const mappedLocale = LOCALE_MAPPING[savedLocale]
    if (mappedLocale) {
      return mappedLocale
    }
  }
  
  // 否则使用浏览器语言
  const browserLang = navigator.language || navigator.userLanguage
  if (browserLang.startsWith('zh')) {
    // 检查是否为繁体中文区域
    if (browserLang.includes('TW') || browserLang.includes('HK') || browserLang.includes('MO')) {
      return 'zh-TW'
    }
    return 'zh'
  }
  return 'en'
}

// 构建消息对象，确保所有语言标识都能找到对应的翻译
const messages = {
  zh,
  en,
  'zh-TW': zhTW
}

// 为每个映射的语言标识添加对应的翻译
Object.keys(LOCALE_MAPPING).forEach(locale => {
  const targetLocale = LOCALE_MAPPING[locale]
  if (!messages[locale] && messages[targetLocale]) {
    messages[locale] = messages[targetLocale]
  }
})

const i18n = createI18n({
  legacy: false, // 使用组合式 API
  locale: getDefaultLocale(),
  fallbackLocale: 'en',
  messages
})

// 提供一个切换语言的方法
export const setLocale = (locale) => {
  // 存储原始的locale值到localStorage
  localStorage.setItem('locale', locale)
  
  // 使用映射后的值设置i18n
  const mappedLocale = LOCALE_MAPPING[locale] || locale
  if (Object.keys(messages).includes(mappedLocale)) {
    i18n.global.locale.value = mappedLocale
  } else if (Object.keys(messages).includes(locale)) {
    i18n.global.locale.value = locale
  }
}

// 提供获取当前语言的方法
export const getCurrentLocale = () => i18n.global.locale.value

// 提供获取原始语言标识的方法
export const getOriginalLocale = () => localStorage.getItem('locale') || getCurrentLocale()

export default i18n 