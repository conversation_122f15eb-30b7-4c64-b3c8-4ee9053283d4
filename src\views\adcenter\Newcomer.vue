<template>
  <nav-bar :title="t('newcomer.title')" />
  <div class="content">
    <div class="banner">
      <img src="@/assets/image/adcenter/newcomer/banner.png" alt="newcomer banner" class="banner-img" />
      <div class="banner-text">
        <div class="banner-title">{{ t('newcomer.banner.title') }}</div>
        <div class="banner-subtitle">{{ t('newcomer.banner.subtitle') }}</div>
      </div>
    </div>

    <div class="claim-button-container">
      <div class="claim-button" @click="handleClaimCoupon" :class="{ 'disabled': isClaiming || !canClaim }">
        {{ buttonText }}
      </div>
    </div>

    <div class="rules-container">
      <h3 class="rules-title">{{ t('newcomer.rules') }}</h3>
      <ul class="rules-list">
        <li><span class="dot"></span>{{ t('newcomer.rulesList.rule1') }}</li>
        <li><span class="dot"></span>{{ t('newcomer.rulesList.rule2') }}</li>
        <li><span class="dot"></span>{{ t('newcomer.rulesList.rule3') }}</li>
        <li><span class="dot"></span>{{ t('newcomer.rulesList.rule4') }}</li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import NavBar from '@/components/NavBar.vue'
import { claimNewUserDiscount } from '@/api/coupon'
import { useUserStore } from '@/stores/user'
import { showToast } from 'vant'

const { t } = useI18n()
const router = useRouter()
const userStore = useUserStore()

// 状态管理
const isClaiming = ref(false)

// 根据用户信息判断是否可以领取新人券
const canClaim = computed(() => {
  return userStore.userInfo.isNewComer === 1
})

// 按钮文本
const buttonText = computed(() => {
  if (isClaiming.value) {
    return t('newcomer.claiming')
  } else if (!canClaim.value) {
    return t('newcomer.notEligible')
  } else {
    return t('newcomer.button')
  }
})

// 处理领取优惠券
const handleClaimCoupon = async () => {
  if (isClaiming.value || !canClaim.value) return

  try {
    isClaiming.value = true

    const response = await claimNewUserDiscount()

    if (response && response.code === 200 && response.data) {
      // 更新用户信息中的新人状态
      if (userStore.userInfo) {
        userStore.setUserInfo({
          ...userStore.userInfo,
          isNewComer: 0
        })
      }

      // 显示成功提示并跳转
      showToast(t('newcomer.claimSuccess.message'))
      setTimeout(() => {
        goToCouponList()
      }, 1500)
    } else {
      showToast(response?.message || t('newcomer.claimFailed'))
    }
  } catch (error) {
    console.error('领取新用户优惠失败:', error)
    showToast(t('newcomer.claimFailed'))
  } finally {
    isClaiming.value = false
  }
}

// 跳转到优惠券列表
const goToCouponList = () => {
  router.push('/adcenter/coupon')
}
</script>

<style scoped>
.banner {
  position: relative;
  width: 100%;
}

.banner-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  margin-bottom: 0.2rem;
}

.banner-text {
  width: 100%;
  text-align: center;
  margin-bottom: 0.2rem;
}

.banner-title {
  font-size: 0.24rem;
  font-weight: bold;
  margin-bottom: 0.08rem;
}

.banner-subtitle {
  font-size: 0.18rem;
}

.claim-button-container {
  padding: 0 0.16rem;
  z-index: 1;
  position: relative;
}

.claim-button {
  width: 100%;
  background-color: #1877F2;
  color: white;
  font-size: 0.18rem;
  padding: 0.1rem 0;
  border-radius: 0.08rem;
  font-weight: bold;
  text-align: center;
}

.claim-button.disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.rules-container {
  padding: 0.16rem;
  margin-top: 0.24rem;
  flex: 1;
}

.rules-title {
  font-size: 0.16rem;
  font-weight: bold;
  margin-bottom: 0.14rem;
  color: #333;
}

.rules-list {
  list-style: none;
  padding: 0;
}

.rules-list li {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0.12rem;
  font-size: 0.14rem;
  line-height: 1.6;
  color: #666;
}

.dot {
  display: inline-block;
  width: 0.08rem;
  height: 0.08rem;
  background-color: #1877F2;
  border-radius: 50%;
  margin-right: 0.1rem;
  margin-top: 0.06rem;
  flex-shrink: 0;
}
</style>