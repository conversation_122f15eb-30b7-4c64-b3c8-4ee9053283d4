<template>
  <!-- TikTok风格全屏布局 -->
  <div class="tiktok-container">
    <!-- 顶部状态栏 -->
    <div class="tiktok-header">
      <div class="header-left">
        <van-icon name="arrow-left" class="back-icon" @click="goBack" />
      </div>
      <div class="header-center">
        <span class="page-title">{{ t('adCenter') }}</span>
      </div>
      <div class="header-right">
        <van-icon name="ellipsis" class="menu-icon" />
      </div>
    </div>

    <!-- 主要内容区域 -->
    <van-pull-refresh 
      v-model="isRefreshing" 
      @refresh="onRefresh" 
      :pull-text="t('pullDown')"
      :loosing-text="t('releaseToRefresh')" 
      :loading-text="t('loading')"
      :success-text="t('refreshSuccess')"
      class="tiktok-refresh"
    >
      <div class="tiktok-content">
        <!-- TikTok风格用户资料区 -->
        <div class="tiktok-profile-section">
          <template v-if="loading">
            <div class="profile-skeleton">
              <van-skeleton title avatar row="3" :row-width="['80%', '60%', '40%']" />
            </div>
          </template>
          <template v-else>
            <!-- 背景渐变 -->
            <div class="profile-background"></div>
            
            <!-- 用户头像和信息 -->
            <div class="profile-content">
              <div class="avatar-section">
                <div class="avatar-container">
                  <van-image 
                    round 
                    width="1.2rem" 
                    height="1.2rem" 
                    :src="userStore.userInfo.fb_avatar" 
                    alt="User Avatar"
                    fit="cover" 
                    class="tiktok-avatar"
                  />
                  <div class="avatar-ring"></div>
                </div>
              </div>
              
              <div class="profile-info">
                <div class="username-section">
                  <h2 class="username">{{ userStore.userInfo.fb_nickname || 'Mabel Truitt' }}</h2>
                  <div class="vip-status">
                    <template v-if="userStore.userInfo.vip_level">
                      <div class="tiktok-vip-badge" @click="router.push('/adcenter/vip-tiktok')">
                        <van-icon name="diamond-o" class="vip-icon" />
                        <span>VIP {{ userStore.userInfo.vip_level }}</span>
                      </div>
                    </template>
                    <template v-else>
                      <div class="activate-vip" @click="router.push('/adcenter/vip-tiktok')">
                        <van-icon name="diamond-o" class="diamond-icon" />
                        <span>{{ t('adcenter.activate') }}</span>
                      </div>
                    </template>
                  </div>
                </div>
                
                <div class="credit-info">
                  <span class="credit-label">{{ t('adcenter.creditScore') }}</span>
                  <span class="credit-value">{{ userStore.userInfo.creditCode || 0 }}</span>
                </div>
              </div>
            </div>
          </template>
        </div>

        <!-- TikTok风格账户信息卡片 -->
        <div class="tiktok-stats-section">
          <template v-if="loading">
            <div class="stats-skeleton">
              <van-skeleton title :row="2" :row-width="['70%', '50%']" />
            </div>
          </template>
          <template v-else>
            <div class="stats-grid">
              <!-- 佣金卡片 -->
              <div class="tiktok-stat-card commission-card" @click="goToCommission">
                <div class="stat-icon-wrapper">
                  <van-icon name="cash-back-record" class="stat-icon" />
                  <div class="icon-glow"></div>
                </div>
                <div class="stat-content">
                  <div class="stat-value">${{ userStore.userInfo.deposit || '0.00' }}</div>
                  <div class="stat-label">{{ t('adcenter.commission') }}</div>
                </div>
                <div class="card-shine"></div>
              </div>
              
              <!-- 钱包卡片 -->
              <div class="tiktok-stat-card wallet-card" @click="goToWallet">
                <div class="stat-icon-wrapper">
                  <van-icon name="balance-o" class="stat-icon" />
                  <div class="icon-glow"></div>
                </div>
                <div class="stat-content">
                  <div class="stat-value">${{ userStore.userInfo.balance || '1000.00' }}</div>
                  <div class="stat-label">{{ t('adcenter.wallet') }}</div>
                </div>
                <div class="card-shine"></div>
              </div>
            </div>
          </template>
        </div>

        <!-- TikTok风格横幅 -->
        <div class="tiktok-banner" @click="goToNewcomer">
          <div class="banner-background">
            <div class="gradient-overlay"></div>
            <img src="@/assets/image/adcenter/banner.png" alt="Newcomer Support" class="banner-image" />
          </div>
          <div class="banner-content">
            <div class="banner-title">{{ t('adcenter.banner.title') }}</div>
            <div class="banner-desc">{{ t('adcenter.banner.desc') }}</div>
            <div class="banner-cta">
              <van-icon name="arrow" class="cta-arrow" />
            </div>
          </div>
        </div>

        <!-- TikTok风格标签页 -->
        <div class="tiktok-tabs-container">
          <div class="tiktok-tabs">
            <div 
              v-for="(tab, index) in tabList" 
              :key="index"
              :class="['tiktok-tab', { active: activeTab === index }]"
              @click="handleTabClick({ index })"
            >
              <span class="tab-text">{{ tab.title }}</span>
              <div v-if="activeTab === index" class="tab-indicator"></div>
            </div>
          </div>
        </div>

        <!-- 数据概览简化版 -->
        <div class="tiktok-overview-section">
          <div class="overview-header">
            <h3 class="section-title">{{ t('adcenter.dataOverview') }}</h3>
            <div class="refresh-button" @click="refreshStatsOverview">
              <van-icon name="replay" class="refresh-icon" :class="{ 'refreshing': isDataRefreshing }" />
              <span class="refresh-text">{{ t('adcenter.dataRefresh') }}</span>
            </div>
          </div>
          
          <div class="tiktok-data-grid">
            <template v-if="loading || isDataRefreshing">
              <div v-for="i in 4" :key="i" class="tiktok-data-card skeleton-card">
                <van-skeleton title :row="1" />
              </div>
            </template>
            <template v-else>
              <!-- 投放金额 -->
              <div class="tiktok-data-card primary-card">
                <div class="card-content">
                  <div class="data-icon-wrapper">
                    <van-icon name="gold-coin-o" class="data-icon" />
                  </div>
                  <div class="data-info">
                    <div class="data-value">$ {{ statsData.amount }}</div>
                    <div class="data-label">{{ t('adcenter.spendAmount') }}</div>
                  </div>
                </div>
              </div>

              <!-- 投放订单 -->
              <div class="tiktok-data-card secondary-card">
                <div class="card-content">
                  <div class="data-icon-wrapper">
                    <van-icon name="bookmark-o" class="data-icon" />
                  </div>
                  <div class="data-info">
                    <div class="data-value">{{ statsData.orders }}</div>
                    <div class="data-label">{{ t('adcenter.adOrders') }}</div>
                  </div>
                </div>
              </div>

              <!-- 已消耗 -->
              <div class="tiktok-data-card accent-card">
                <div class="card-content">
                  <div class="data-icon-wrapper">
                    <van-icon name="refund-o" class="data-icon" />
                  </div>
                  <div class="data-info">
                    <div class="data-value">$ {{ statsData.spent }}</div>
                    <div class="data-label">{{ t('adcenter.consumed') }}</div>
                  </div>
                </div>
              </div>

              <!-- 利润 -->
              <div class="tiktok-data-card profit-card">
                <div class="card-content">
                  <div class="data-icon-wrapper">
                    <van-icon name="balance-pay" class="data-icon" />
                  </div>
                  <div class="data-info">
                    <div class="data-value">$ {{ statsData.profit }}</div>
                    <div class="data-label">{{ t('adcenter.profit') }}</div>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>

        <!-- TikTok风格功能导航 -->
        <div class="tiktok-features-section">
          <div class="features-grid">
            <div class="tiktok-feature-card" @click="goToAgencyService">
              <div class="feature-content">
                <div class="feature-icon-container">
                  <van-icon name="chart-trending-o" class="feature-icon" />
                </div>
                <div class="feature-text">{{ t('adcenter.operation') }}</div>
              </div>
            </div>
            
            <div class="tiktok-feature-card" @click="goToCoupons">
              <div class="feature-content">
                <div class="feature-icon-container">
                  <van-icon name="coupon-o" class="feature-icon" />
                </div>
                <div class="feature-text">{{ t('adcenter.coupon') }}</div>
              </div>
            </div>
            
            <div class="tiktok-feature-card" @click="goToFeedback">
              <div class="feature-content">
                <div class="feature-icon-container">
                  <van-icon name="edit" class="feature-icon" />
                </div>
                <div class="feature-text">{{ t('adcenter.feedback') }}</div>
              </div>
            </div>
            
            <div class="tiktok-feature-card" @click="goToCustomerService">
              <div class="feature-content">
                <div class="feature-icon-container">
                  <van-icon name="service-o" class="feature-icon" />
                </div>
                <div class="feature-text">{{ t('adcenter.service') }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- TikTok风格FAQ部分 -->
        <div class="tiktok-faq-section">
          <h3 class="faq-title">{{ t('adcenter.faq') }}</h3>
          <div class="faq-container">
            <div class="tiktok-faq-item" @click="goToFAQ(0)">
              <div class="faq-content">
                <div class="faq-text">{{ t('adcenter.faqList.whatIsAdUnion') }}</div>
                <van-icon name="arrow" class="faq-arrow" />
              </div>
            </div>
            
            <div class="tiktok-faq-item" @click="goToFAQ(1)">
              <div class="faq-content">
                <div class="faq-text">{{ t('adcenter.faqList.whyCantCancel') }}</div>
                <van-icon name="arrow" class="faq-arrow" />
              </div>
            </div>
            
            <div class="tiktok-faq-item" @click="goToFAQ(2)">
              <div class="faq-content">
                <div class="faq-text">{{ t('adcenter.faqList.onlineRecharge') }}</div>
                <van-icon name="arrow" class="faq-arrow" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </van-pull-refresh>
  </div>
</template>

<script setup>
import { useTitle } from '@/utils/useTitle'
import { useI18n } from 'vue-i18n'
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import {
  Icon as VanIcon,
  Image as VanImage,
  Skeleton as VanSkeleton,
  PullRefresh as VanPullRefresh,
} from 'vant'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { getUserInfo, loginAndGetUserInfo } from '@/utils/useUserInfo'
import { getStatsOverview } from '@/api/adCenter'

const { t, locale } = useI18n()
const activeTab = ref(0)
const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const loading = ref(true)
const isRefreshing = ref(false)
const refreshTimer = ref(null)
const isDataRefreshing = ref(false)

// TikTok风格标签页数据
const tabList = computed(() => [
  { title: t('adcenter.tabs.today') },
  { title: t('adcenter.tabs.thisWeek') },
  { title: t('adcenter.tabs.thisMonth') },
  { title: t('adcenter.tabs.all') }
])

const statsData = ref({
  impressions: 0,
  clicks: 0,
  amount: '0.00',
  orders: 0,
  spent: '0.00',
  pending: '0.00',
  revenue: '0.00',
  profit: '0.00'
})

// 获取数据概览信息
const fetchStatsOverview = async () => {
  try {
    isDataRefreshing.value = true
    const periodMap = {
      0: 'today',
      1: 'week',
      2: 'month',
      3: 'all'
    }
    const period = periodMap[activeTab.value] || 'today'

    const res = await getStatsOverview(period)
    if (res && res.data) {
      statsData.value = res.data
    }
  } catch (error) {
    console.error('获取数据概览失败:', error)
  } finally {
    isDataRefreshing.value = false
  }
}

const goBack = () => {
  const userAgent = navigator.userAgent.toLowerCase();
  if (userAgent.indexOf('android') !== -1) {
    if (window.AppTweak) {
      window.AppTweak.ClosePage();
    }
  } else if (userAgent.indexOf('iphone') !== -1 || userAgent.indexOf('ipad') !== -1) {
    if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.AppTweak) {
      window.webkit.messageHandlers.AppTweak.postMessage("ClosePage");
    }
  }
}

// 手动刷新数据概览
const refreshStatsOverview = () => {
  if (isDataRefreshing.value) return
  fetchStatsOverview()
}

// 下拉刷新处理函数
const onRefresh = async () => {
  try {
    loading.value = true
    const userInfo = await getUserInfo(true)
    if (!userInfo) {
      console.error('刷新用户信息失败')
    }
    await fetchStatsOverview()
  } catch (error) {
    console.error('刷新数据失败:', error)
  } finally {
    isRefreshing.value = false
    loading.value = false
  }
}

// 处理标签点击
const handleTabClick = (tab) => {
  activeTab.value = tab.index;
  fetchStatsOverview();
}

// 跳转函数
const goToFAQ = (id) => {
  router.push({
    path: '/adcenter/faq',
    query: { id }
  })
}

const goToAgencyService = () => router.push('/adcenter/agent')
const goToCoupons = () => router.push('/adcenter/coupon')
const goToFeedback = () => router.push('/adcenter/feedback')
const goToCustomerService = () => router.push('/adcenter/service')
const goToWallet = () => router.push('/adcenter/wallet-tiktok')
const goToCommission = () => router.push('/adcenter/commission-tiktok')
const goToNewcomer = () => router.push('/adcenter/newcomer')

// 初始化数据
onMounted(async () => {
  loading.value = true
  try {
    const dataParam = route.query.data

    if (!dataParam) {
      const userInfo = await getUserInfo()
      if (!userInfo) {
        console.error('获取用户信息失败')
      }
    } else {
      const userInfo = await loginAndGetUserInfo(dataParam)
      if (userInfo) {
        locale.value = userInfo.fb_lang
        router.push('/adcenter')
      } else {
        console.error('登录失败或获取用户信息失败')
      }
    }

    if (!userStore.userInfo.auth_code) {
      router.push('/account?url=adcenter')
    }

    await fetchStatsOverview()
  } catch (error) {
    console.error('请求数据失败:', error)
  } finally {
    loading.value = false
  }
})

// 设置页面标题
useTitle(() => t('adCenter'))
</script>

<style scoped>
/* TikTok风格全局样式 */
.tiktok-container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(180deg, #000000 0%, #1a1a1a 50%, #000000 100%);
  color: #ffffff;
  overflow-x: hidden;
}

/* TikTok风格顶部导航 */
.tiktok-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 0.6rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 0.16rem;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 1000;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-left,
.header-right {
  width: 0.4rem;
  display: flex;
  justify-content: center;
}

.header-center {
  flex: 1;
  text-align: center;
}

.back-icon,
.menu-icon {
  font-size: 0.24rem;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-icon:active,
.menu-icon:active {
  transform: scale(0.9);
  color: #ff0050;
}

.page-title {
  font-size: 0.18rem;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

/* TikTok风格下拉刷新 */
.tiktok-refresh {
  padding-top: 0.6rem;
  min-height: 100vh;
}

.tiktok-content {
  padding: 0.2rem 0.16rem 0.4rem;
}

/* TikTok风格用户资料区 */
.tiktok-profile-section {
  position: relative;
  margin-bottom: 0.24rem;
  border-radius: 0.2rem;
  overflow: hidden;
  background: linear-gradient(135deg, #ff0050 0%, #ff4081 50%, #9c27b0 100%);
  box-shadow: 0 8px 32px rgba(255, 0, 80, 0.3);
}

.profile-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 0, 80, 0.9) 0%, rgba(156, 39, 176, 0.9) 100%);
  backdrop-filter: blur(20px);
}

.profile-skeleton {
  padding: 0.24rem;
  min-height: 1.2rem;
}

.profile-skeleton :deep(.van-skeleton) {
  background: rgba(255, 255, 255, 0.1);
}

.profile-content {
  position: relative;
  padding: 0.24rem;
  display: flex;
  align-items: center;
  gap: 0.16rem;
}

.avatar-section {
  position: relative;
}

.avatar-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tiktok-avatar {
  border: 3px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

.avatar-ring {
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border: 2px solid transparent;
  border-radius: 50%;
  background: linear-gradient(45deg, #ff0050, #ff4081, #9c27b0, #ff0050);
  background-size: 400% 400%;
  animation: avatarGlow 3s ease-in-out infinite;
  z-index: -1;
}

@keyframes avatarGlow {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.profile-info {
  flex: 1;
}

.username-section {
  margin-bottom: 0.12rem;
}

.username {
  font-size: 0.22rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 0.08rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.vip-status {
  display: flex;
  align-items: center;
}

.tiktok-vip-badge {
  display: flex;
  align-items: center;
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  color: #000;
  border-radius: 0.2rem;
  padding: 0.06rem 0.12rem;
  font-size: 0.12rem;
  font-weight: 700;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
  cursor: pointer;
  transition: all 0.3s ease;
}

.tiktok-vip-badge:active {
  transform: scale(0.95);
}

.vip-icon {
  font-size: 0.14rem;
  margin-right: 0.04rem;
}

.activate-vip {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
  border-radius: 0.2rem;
  padding: 0.06rem 0.12rem;
  font-size: 0.12rem;
  font-weight: 600;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.activate-vip:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.3);
}

.diamond-icon {
  font-size: 0.14rem;
  margin-right: 0.04rem;
}

.credit-info {
  display: flex;
  align-items: center;
  gap: 0.08rem;
  background: rgba(0, 0, 0, 0.3);
  padding: 0.06rem 0.12rem;
  border-radius: 0.12rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.credit-label {
  font-size: 0.12rem;
  color: rgba(255, 255, 255, 0.8);
}

.credit-value {
  font-size: 0.14rem;
  font-weight: 700;
  color: #00ff88;
  text-shadow: 0 0 8px rgba(0, 255, 136, 0.5);
}

/* TikTok风格账户信息卡片 */
.tiktok-stats-section {
  margin-bottom: 0.24rem;
}

.stats-skeleton {
  padding: 0.2rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0.16rem;
  backdrop-filter: blur(10px);
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.16rem;
}

.tiktok-stat-card {
  position: relative;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-radius: 0.16rem;
  padding: 0.2rem;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
}

.tiktok-stat-card:active {
  transform: scale(0.98);
}

.commission-card {
  background: linear-gradient(135deg, rgba(255, 0, 80, 0.2) 0%, rgba(255, 64, 129, 0.1) 100%);
  border-color: rgba(255, 0, 80, 0.3);
}

.wallet-card {
  background: linear-gradient(135deg, rgba(0, 255, 136, 0.2) 0%, rgba(76, 175, 80, 0.1) 100%);
  border-color: rgba(0, 255, 136, 0.3);
}

.stat-icon-wrapper {
  position: relative;
  width: 0.4rem;
  height: 0.4rem;
  border-radius: 0.12rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.12rem;
  background: rgba(255, 255, 255, 0.1);
}

.stat-icon {
  font-size: 0.2rem;
  color: #ffffff;
}

.icon-glow {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #ff0050, #00ff88);
  border-radius: 0.14rem;
  opacity: 0.6;
  z-index: -1;
  animation: iconPulse 2s ease-in-out infinite;
}

@keyframes iconPulse {
  0%, 100% { opacity: 0.6; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.05); }
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 0.18rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 0.04rem;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.stat-label {
  font-size: 0.12rem;
  color: rgba(255, 255, 255, 0.8);
}

.card-shine {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  animation: cardShine 3s ease-in-out infinite;
}

@keyframes cardShine {
  0%, 100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* TikTok风格横幅 */
.tiktok-banner {
  position: relative;
  margin-bottom: 0.24rem;
  border-radius: 0.2rem;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tiktok-banner:active {
  transform: scale(0.98);
}

.banner-background {
  position: relative;
  height: 1rem;
  background: linear-gradient(135deg, #ff0050 0%, #ff4081 50%, #9c27b0 100%);
}

.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.1) 100%);
}

.banner-image {
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: auto;
  opacity: 0.8;
}

.banner-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 0.2rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  z-index: 2;
}

.banner-title {
  font-size: 0.2rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 0.08rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.banner-desc {
  font-size: 0.14rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 0.12rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.banner-cta {
  display: flex;
  align-items: center;
}

.cta-arrow {
  font-size: 0.16rem;
  color: #ffffff;
  animation: arrowBounce 2s ease-in-out infinite;
}

@keyframes arrowBounce {
  0%, 100% { transform: translateX(0); }
  50% { transform: translateX(4px); }
}

/* TikTok风格标签页 */
.tiktok-tabs-container {
  margin-bottom: 0.24rem;
}

.tiktok-tabs {
  display: flex;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0.16rem;
  padding: 0.04rem;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.tiktok-tab {
  position: relative;
  flex: 1;
  padding: 0.12rem 0.16rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 0.12rem;
}

.tiktok-tab.active {
  background: linear-gradient(135deg, #ff0050, #ff4081);
  box-shadow: 0 4px 12px rgba(255, 0, 80, 0.4);
}

.tab-text {
  font-size: 0.14rem;
  font-weight: 600;
  color: #ffffff;
  position: relative;
  z-index: 2;
}

.tab-indicator {
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 0.2rem;
  height: 0.03rem;
  background: #ffffff;
  border-radius: 0.02rem;
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.8);
}

/* TikTok风格数据概览 */
.tiktok-overview-section {
  margin-bottom: 0.24rem;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.16rem;
}

.section-title {
  font-size: 0.18rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.refresh-button {
  display: flex;
  align-items: center;
  gap: 0.06rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.08rem 0.12rem;
  border-radius: 0.12rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
}

.refresh-button:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.2);
}

.refresh-icon {
  font-size: 0.14rem;
  color: #00ff88;
}

.refreshing {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.refresh-text {
  font-size: 0.12rem;
  color: rgba(255, 255, 255, 0.8);
}

.tiktok-data-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.12rem;
}

.tiktok-data-card {
  position: relative;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-radius: 0.16rem;
  padding: 0.16rem;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  transition: all 0.3s ease;
  min-height: 0.8rem;
}

.tiktok-data-card:active {
  transform: scale(0.98);
}

.skeleton-card {
  background: rgba(255, 255, 255, 0.05);
}

.skeleton-card :deep(.van-skeleton) {
  background: rgba(255, 255, 255, 0.1);
}

/* 数据卡片不同主题色 */
.primary-card {
  background: linear-gradient(135deg, rgba(255, 0, 80, 0.2) 0%, rgba(255, 64, 129, 0.1) 100%);
  border-color: rgba(255, 0, 80, 0.3);
}

.secondary-card {
  background: linear-gradient(135deg, rgba(0, 255, 136, 0.2) 0%, rgba(76, 175, 80, 0.1) 100%);
  border-color: rgba(0, 255, 136, 0.3);
}

.accent-card {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.2) 0%, rgba(255, 235, 59, 0.1) 100%);
  border-color: rgba(255, 193, 7, 0.3);
}

.profit-card {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.2) 0%, rgba(255, 193, 7, 0.1) 100%);
  border-color: rgba(255, 215, 0, 0.3);
}

.card-content {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.data-icon-wrapper {
  position: relative;
  width: 0.32rem;
  height: 0.32rem;
  border-radius: 0.08rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.12rem;
  background: rgba(255, 255, 255, 0.2);
}

.data-icon {
  font-size: 0.18rem;
  color: #ffffff;
}

.data-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.data-value {
  font-size: 0.16rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 0.04rem;
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
}

.data-label {
  font-size: 0.11rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.2;
}

/* TikTok风格功能导航 */
.tiktok-features-section {
  margin-bottom: 0.24rem;
}

.features-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.16rem;
}

.tiktok-feature-card {
  position: relative;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-radius: 0.16rem;
  padding: 0.2rem;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
}

.tiktok-feature-card:active {
  transform: scale(0.95);
}

.feature-content {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.feature-icon-container {
  position: relative;
  width: 0.48rem;
  height: 0.48rem;
  border-radius: 0.12rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.12rem;
  background: rgba(255, 255, 255, 0.1);
}

.feature-icon {
  font-size: 0.24rem;
  color: #ffffff;
}

.feature-text {
  font-size: 0.12rem;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* TikTok风格FAQ部分 */
.tiktok-faq-section {
  margin-bottom: 0.24rem;
}

.faq-title {
  font-size: 0.18rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 0.16rem 0;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.faq-container {
  display: flex;
  flex-direction: column;
  gap: 0.12rem;
}

.tiktok-faq-item {
  position: relative;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-radius: 0.16rem;
  padding: 0.16rem;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
}

.tiktok-faq-item:active {
  transform: scale(0.98);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%);
}

.faq-content {
  position: relative;
  z-index: 2;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.faq-text {
  font-size: 0.14rem;
  color: #ffffff;
  font-weight: 500;
  flex: 1;
  margin-right: 0.12rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.faq-arrow {
  font-size: 0.16rem;
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.tiktok-faq-item:active .faq-arrow {
  color: #ff0050;
  transform: translateX(2px);
}

/* 下拉刷新样式覆盖 */
.tiktok-refresh :deep(.van-pull-refresh__track) {
  background: transparent;
}

.tiktok-refresh :deep(.van-pull-refresh__head) {
  color: #ffffff;
}

/* 骨架屏样式覆盖 */
.tiktok-container :deep(.van-skeleton) {
  background: rgba(255, 255, 255, 0.1);
}

.tiktok-container :deep(.van-skeleton__avatar) {
  background: rgba(255, 255, 255, 0.2);
}

.tiktok-container :deep(.van-skeleton__title) {
  background: rgba(255, 255, 255, 0.15);
}

.tiktok-container :deep(.van-skeleton__row) {
  background: rgba(255, 255, 255, 0.1);
}
</style>
