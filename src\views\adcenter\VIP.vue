<template>
  <nav-bar :title="t('vip.title')" />
  <div class="content">
    <!-- 加载状态 -->
    <template v-if="pageLoading">
      <div class="skeleton-container">
        <!-- VIP总览骨架屏 -->
        <div class="vip-overview skeleton">
          <van-skeleton avatar avatar-size="0.5rem" title :row="1" />
        </div>
        
        <!-- VIP列表骨架屏 -->
        <div class="vip-list">
          <div class="vip-item skeleton" v-for="i in 3" :key="i">
            <div class="vip-header skeleton">
              <van-skeleton avatar avatar-size="0.4rem" title :row="0" />
            </div>
            <div class="vip-content">
              <van-skeleton title :row="3" />
            </div>
          </div>
        </div>
      </div>
    </template>

    <template v-else>
      <!-- VIP总览卡片 -->
      <div class="vip-overview" v-if="userVip">
        <div class="vip-icon">
          <img :src="getVipIcon(userVip.level)" alt="VIP图标" />
        </div>
        <div class="vip-info">
          <h3>VIP {{ userVip.level }}</h3>
          <p>{{ t('vip.currentVip') }}</p>
        </div>
      </div>

      <!-- VIP特权入口按钮 -->
      <div class="vip-entry" v-if="!userVip">
        <van-button type="primary" block icon="diamond-o" @click="showAllVips">
          {{ t('vip.openVip') }}
        </van-button>
      </div>

      <!-- VIP列表 -->
      <div class="vip-list">
        <div class="vip-item" v-for="vip in vipList" :key="vip.id" :class="{'current-vip': vip.isCurrent}">
          <div class="vip-header">
            <div class="vip-icon">
              <img :src="getVipIcon(vip.level)" alt="VIP图标" />
              <!-- <img src="@/assets/image/adcenter/vip/vip1.jpg" alt="Newcomer Support" /> -->
            </div>
            <div class="vip-title">
              <span>VIP {{ vip.level }}</span>
              <van-tag type="warning" v-if="vip.tag">{{ vip.tag }}</van-tag>
              <van-tag type="success" v-if="vip.isCurrent">{{ t('vip.currentLevel') }}</van-tag>
            </div>
            <div class="vip-price">${{ vip.price }}</div>
          </div>

          <div class="vip-content">
            <div class="vip-detail">
              <div class="vip-detail-item">
                <div class="label">
                  <van-icon name="balance-o" />
                  {{ t('vip.commissionRatio') }}
                </div>
                <div class="value">{{ vip.commission }}</div>
              </div>
              <div class="vip-detail-item">
                <div class="label">
                  <van-icon name="friends-o" />
                  {{ t('vip.downlineLimit') }}
                </div>
                <div class="value">{{ vip.downLineLimit }}</div>
              </div>
            </div>

            <div class="vip-features">
              <div class="feature-item" v-for="(feature, index) in vip.features" :key="index">
                <van-icon name="success" color="#1877F2" />
                <span>{{ feature }}</span>
              </div>
            </div>

            <van-button 
              type="primary" 
              block 
              class="activate-btn" 
              @click="showVipConfirm(vip)"
              :disabled="!canUpgrade(vip.level)"
            >
              {{ vip.isCurrent ? t('vip.alreadyActivated') : (!vip.canUpgrade ? t('vip.noNeedToActivate') : t('vip.activateNow')) }}
            </van-button>
          </div>
        </div>
      </div>
    </template>
  </div>

  <!-- VIP开通确认弹窗 -->
  <van-dialog
    v-model:show="showConfirmDialog"
    :title="t('vip.confirmUpgrade')"
    show-cancel-button
    :confirm-button-text="t('vip.confirmUpgrade')"
    :cancel-button-text="t('vip.cancel')"
    @confirm="handleConfirmUpgrade"
    :loading="loading"
  >
    <div class="confirm-content">
      <p>{{ t('vip.upgradeToVip', { level: selectedVip?.level }) }}</p>
      <p>{{ t('vip.activationFee', { price: selectedVip?.price }) }}</p>
    </div>
  </van-dialog>

  <!-- 交易密码验证组件 -->
  <transaction-password-verify
    v-model:show="showPasswordVerify"
    :title="t('vip.paymentPasswordTitle')"
    :tip-text="t('vip.paymentPasswordTip')"
    @confirm="confirmVipUpgrade"
    @cancel="cancelPasswordVerify"
    @verification-error="handleVerificationError"
  />
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import NavBar from '@/components/NavBar.vue'
import { Dialog, Button as VanButton, Icon as VanIcon, Tag as VanTag, Loading as VanLoading, showToast, showFailToast, showSuccessToast, Skeleton as VanSkeleton } from 'vant'
import { vipApi } from '@/api'
import { useI18n } from 'vue-i18n'
import TransactionPasswordVerify from '@/components/TransactionPasswordVerify.vue'
import { getUserInfo } from '@/utils/useUserInfo'

// 国际化
const { t } = useI18n()

// 选中的VIP信息
const selectedVip = ref(null)

// 确认弹窗显示状态
const showConfirmDialog = ref(false)

// 交易密码验证弹窗显示状态
const showPasswordVerify = ref(false)

// 加载状态
const loading = ref(false)

// VIP列表数据
const vipList = ref([])

// 页面加载状态
const pageLoading = ref(false)

// 获取VIP列表
const fetchVipList = async () => {
  try {
    pageLoading.value = true
    const response = await vipApi.getVipList()
    const data = response.data || []
    vipList.value = data.map(item => ({
      id: item.id,
      level: item.level,
      name: item.name,
      tag: item.name,
      price: item.price,
      commission: formatCommissionRatio(item.commission_ratios),
      downLineLimit: item.subordinate_limit === 0 ? t('vip.unlimited') : String(item.subordinate_limit),
      features: formatFeatures(item),
      icon: item.icon,
      isCurrent: item.is_current,
      canUpgrade: item.can_upgrade
    }))
  } catch (error) {
    console.error('获取VIP列表失败:', error)
    showFailToast(error.message || t('vip.loadFailed'))
  } finally {
    pageLoading.value = false
  }
}

// 格式化分佣比例
const formatCommissionRatio = (ratios) => {
  if (!ratios) return '0%'
  
  // 如果有level1的比例，显示level1的比例
  if (ratios.level1) {
    return (ratios.level1 * 100).toFixed(1) + '%'
  }
  
  return '0%'
}

// 格式化VIP特权描述
const formatFeatures = (vip) => {
  let features = []
  
  // 如果description是数组，直接使用
  if (Array.isArray(vip.description)) {
    features = [...vip.description]
  } 
  // 如果是字符串，添加到数组中
  else if (vip.description) {
    features.push(vip.description)
  }
  
  // 如果有特权列表，添加到features中
  if (Array.isArray(vip.privileges)) {
    features = [...features, ...vip.privileges]
  }
  
  return features
}

// 用户当前VIP信息
const userVip = computed(() => {
  if (vipList.value.length === 0) return null
  return vipList.value.find(vip => vip.isCurrent) || null
})

// 获取VIP图标
const getVipIcon = (level) => {
  const vip = vipList.value.find(v => v.level === level)
  if (vip && vip.icon) {
    return vip.icon
  }
  // 使用默认图标
  return `/src/assets/image/adcenter/vip/vip${level}.jpg`
}

// 显示所有VIP信息
const showAllVips = () => {
  // 如果需要在点击按钮时处理特殊逻辑，可以在这里实现
}

// 显示VIP确认弹窗
const showVipConfirm = (vip) => {
  // 检查是否可以升级
  if (!vip.canUpgrade) {
    Dialog.alert({
      title: t('tips'),
      message: t('vip.alreadyVip', { level: userVip.value?.level || 0 }),
    })
    return
  }
  
  selectedVip.value = vip
  showConfirmDialog.value = true
}

// 处理确认升级按钮点击
const handleConfirmUpgrade = () => {
  showConfirmDialog.value = false
  // 显示交易密码验证弹窗
  showPasswordVerify.value = true
}

// 取消密码验证
const cancelPasswordVerify = () => {
  showToast(t('vip.upgradeCanceled'))
}

// 处理验证错误
const handleVerificationError = ({ message }) => {
  showFailToast(message)
}

// 确认VIP升级
const confirmVipUpgrade = async (password) => {
  if (!selectedVip.value) return
  
  try {
    loading.value = true
    
    // 调用VIP升级接口
    await vipApi.upgradeVip({ 
      vip_id: selectedVip.value.id,
      payment_password: password
    })
    
    // 关闭密码验证弹窗
    showPasswordVerify.value = false
    
    // 升级成功后更新用户信息
    await getUserInfo(true) // 强制更新用户信息
    
    // 重新获取VIP列表
    await fetchVipList()
    
    // 显示成功提示
    showSuccessToast(t('vip.upgradeSuccess'))
    
  } catch (error) {
    console.error('VIP升级失败:', error)
    
    // 显示错误提示
    showFailToast(error.message || t('vip.upgradeFailed'))
    
  } finally {
    loading.value = false
  }
}

// 判断是否可以升级到指定VIP等级
const canUpgrade = (level) => {
  const vip = vipList.value.find(v => v.level === level)
  return vip ? vip.canUpgrade : false
}

// 页面加载时获取VIP列表
onMounted(() => {
  fetchVipList()
})
</script>

<style scoped>
.content {
  padding: 0.16rem;
  background-color: #f5f5f5;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 3rem;
}

/* 骨架屏样式 */
.skeleton-container {
  animation: fade-in 0.3s;
}

.vip-overview.skeleton {
  padding: 0.16rem;
  background-color: white;
  border-radius: 0.1rem;
  margin-bottom: 0.16rem;
  overflow: hidden;
}

.vip-item.skeleton {
  margin-bottom: 0.16rem;
  background-color: white;
  border-radius: 0.1rem;
  overflow: hidden;
}

.vip-header.skeleton {
  padding: 0.16rem;
  border-bottom: 0.01rem solid #f5f5f5;
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.vip-overview {
  display: flex;
  align-items: center;
  padding: 0.16rem;
  background-color: #1877F2;
  color: white;
  border-radius: 0.1rem;
  margin-bottom: 0.16rem;
}

.vip-overview .vip-icon {
  width: 0.5rem;
  height: 0.5rem;
  margin-right: 0.16rem;
  overflow: hidden;
}

.vip-overview .vip-icon img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.vip-overview .vip-info h3 {
  font-size: 0.18rem;
  margin: 0;
}

.vip-overview .vip-info p {
  font-size: 0.14rem;
  margin: 0.05rem 0 0;
  opacity: 0.9;
}

.vip-entry {
  margin-bottom: 0.16rem;
}

.vip-list {
  display: flex;
  flex-direction: column;
  gap: 0.16rem;
}

.vip-item {
  background-color: white;
  border-radius: 0.1rem;
  overflow: hidden;
  box-shadow: 0 0.02rem 0.05rem rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

/* 当前VIP等级卡片样式 */
.vip-item.current-vip {
  border: 0.02rem solid #1877F2;
  transform: scale(1.01);
  box-shadow: 0 0.04rem 0.1rem rgba(24, 119, 242, 0.15);
}

.vip-header {
  display: flex;
  align-items: center;
  padding: 0.16rem;
  border-bottom: 0.01rem solid #f5f5f5;
}

.vip-header .vip-icon {
  width: 0.4rem;
  height: 0.4rem;
  margin-right: 0.1rem;
}

.vip-header .vip-icon img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.vip-header .vip-title {
  display: flex;
  align-items: center;
  flex: 1;
  font-weight: bold;
}

.vip-header .vip-title span {
  margin-right: 0.1rem;
}

.vip-header .vip-price {
  font-size: 0.2rem;
  font-weight: bold;
  color: #ff5252;
}

.vip-content {
  padding: 0.16rem;
}

.vip-detail {
  display: flex;
  margin-bottom: 0.16rem;
  background-color: #f5f5f5;
  padding: 0.1rem;
  border-radius: 0.075rem;
}

.vip-detail-item {
  flex: 1;
  text-align: center;
}

.vip-detail-item .label {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 0.12rem;
  margin-bottom: 0.05rem;
}

.vip-detail-item .label .van-icon {
  margin-right: 0.05rem;
}

.vip-detail-item .value {
  font-size: 0.14rem;
  font-weight: bold;
}

.vip-features {
  margin-bottom: 0.16rem;
  background-color: #f5f5f5;
  padding: 0.1rem;
  border-radius: 0.075rem;
}

.feature-item {
  display: flex;
  margin-bottom: 0.1rem;
  font-size: 0.14rem;
  line-height: 1.4;
}

.feature-item .van-icon {
  margin-right: 0.05rem;
  flex-shrink: 0;
  margin-top: 0.02rem;
}

.activate-btn {
  margin-top: 0.1rem;
}

.confirm-content {
  padding: 0.2rem 0;
  text-align: center;
}

.confirm-content p {
  margin: 0.1rem 0;
}

/* Facebook风格的主题色 */
:deep(.van-button--primary) {
  background-color: #1877F2;
  border-color: #1877F2;
}

:deep(.van-dialog__confirm) {
  color: #1877F2;
}

/* 禁用按钮样式 */
:deep(.van-button--disabled) {
  opacity: 0.6;
}
</style> 