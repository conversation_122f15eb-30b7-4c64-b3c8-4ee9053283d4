import { createRouter, createWebHistory, createWebHashHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/Home.vue'),
    meta: { disableSwipeBack: true, keepAlive: true }
  },
  {
    path: '/account',
    name: 'Account',
    component: () => import('@/views/Account.vue'),
    meta: { disableSwipeBack: true, keepAlive: true }
  },
  {
    path: '/adcenter',
    name: 'AdCenter',
    component: () => import('@/views/AdCenter.vue'),
    meta: { disableSwipeBack: true, keepAlive: true }
  },
  {
    path: '/adcenter-tiktok',
    name: 'AdCenterTikTok',
    component: () => import('@/views/AdCenter_TikTok.vue'),
    meta: { disableSwipeBack: true, keepAlive: true }
  },
  // 广告中心子页面独立路由
  {
    path: '/adcenter/agent',
    name: 'Agent',
    component: () => import('@/views/adcenter/Agent.vue'),
    meta: { requiresAuth: true, keepAlive: true }
  },
  {
    path: '/adcenter/newcomer',
    name: 'Newcomer',
    component: () => import('@/views/adcenter/Newcomer.vue'),
    meta: { requiresAuth: true, keepAlive: true }
  },
  {
    path: '/adcenter/coupon',
    name: 'Coupon',
    component: () => import('@/views/adcenter/Coupon.vue'),
    meta: { requiresAuth: true, keepAlive: true }
  },
  {
    path: '/adcenter/vip',
    name: 'VIP',
    component: () => import('@/views/adcenter/VIP.vue'),
    meta: { requiresAuth: true, keepAlive: true }
  },
  {
    path: '/adcenter/vip-tiktok',
    name: 'VIPTikTok',
    component: () => import('@/views/adcenter/VIP_TikTok.vue'),
    meta: { requiresAuth: true, keepAlive: true }
  },
  {
    path: '/adcenter/wallet',
    name: 'Wallet',
    component: () => import('@/views/adcenter/Wallet.vue'),
    meta: { requiresAuth: true, keepAlive: true }
  },
  {
    path: '/adcenter/wallet-tiktok',
    name: 'WalletTikTok',
    component: () => import('@/views/adcenter/Wallet_TikTok.vue'),
    meta: { requiresAuth: true, keepAlive: true }
  },
  {
    path: '/adcenter/commission',
    name: 'Commission',
    component: () => import('@/views/adcenter/Commission.vue'),
    meta: { requiresAuth: true, keepAlive: true }
  },
  {
    path: '/adcenter/commission-tiktok',
    name: 'CommissionTikTok',
    component: () => import('@/views/adcenter/Commission_TikTok.vue'),
    meta: { requiresAuth: true, keepAlive: true }
  },
  {
    path: '/adcenter/faq',
    name: 'FAQ',
    component: () => import('@/views/adcenter/FAQ.vue'),
    meta: { requiresAuth: true, keepAlive: true }
  },
  {
    path: '/adcenter/wallet/bank',
    name: 'BankCard',
    component: () => import('@/views/adcenter/wallet/BankCard.vue'),
    meta: { requiresAuth: true, keepAlive: true }
  },
  {
    path: '/adcenter/wallet/crypto',
    name: 'Crypto',
    component: () => import('@/views/adcenter/wallet/Crypto.vue'),
    meta: { requiresAuth: true, keepAlive: true }
  },
  {
    path: '/adcenter/wallet/password',
    name: 'Password',
    component: () => import('@/views/adcenter/wallet/Password.vue'),
    meta: { requiresAuth: true, keepAlive: true }
  },
  {
    path: '/adcenter/notification',
    name: 'Notification',
    component: () => import('@/views/adcenter/Notification.vue'),
    meta: { requiresAuth: true, keepAlive: true }
  },
  {
    path: '/adcenter/feedback',
    name: 'Feedback',
    component: () => import('@/views/adcenter/Feedback.vue'),
    meta: { requiresAuth: true, keepAlive: true }
  },
  {
    path: '/adcenter/service',
    name: 'Service',
    component: () => import('@/views/adcenter/Service.vue'),
    meta: { requiresAuth: true, keepAlive: true }
  },
  {
    path: '/plans',
    name: 'Plans',
    component: () => import('@/views/Plans.vue'),
    meta: { disableSwipeBack: true, keepAlive: true }
  },
  {
    path: '/plans/plan/:id',
    name: 'PlanDetail',
    component: () => import('@/views/plans/PlanDetail.vue'),
    meta: { keepAlive: true }
  },
  {
    path: '/plans/product/:productId',
    name: 'ProductDetail',
    component: () => import('@/views/plans/plan/ProductDetail.vue'),
    meta: { keepAlive: true }
  }
]

const router = createRouter({
  // history: createWebHistory(),
  history: createWebHashHistory(),
  routes,
  // 自定义滚动行为
  scrollBehavior(to, from, savedPosition) {
    // 如果有保存的位置，则恢复到保存的位置
    if (savedPosition) {
      return savedPosition;
    }
    // 否则滚动到顶部
    return { top: 0 };
  },
  // 添加标记，用于判断是否为后退操作
  isBack: false
})

// 路由历史记录，用于判断前进/后退
const routeHistory = []

// 全局路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()
  // 如果页面需要登录且用户未登录
  if (to.meta.requiresAuth && !userStore.isLogin) {
    // 跳转到登录页或账户开通页
    next('/')
  } else {
    // 记录路由历史，用于判断前进/后退
    if (from.path) {
      routeHistory.push(from.path)
    }
    next()
  }
})

// 路由切换后的钩子，用于处理页面过渡效果
router.afterEach((to, from) => {
  // 页面切换后，滚动到顶部
  window.scrollTo(0, 0);
})

// 重写router.back方法，添加标记
const originalBack = router.back
router.back = function() {
  router.options.isBack = true
  originalBack.call(this)
}

// 重写router.go方法，添加标记
const originalGo = router.go
router.go = function(delta) {
  if (delta < 0) {
    router.options.isBack = true
  }
  originalGo.call(this, delta)
}

export default router 