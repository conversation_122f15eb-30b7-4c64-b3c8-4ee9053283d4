<template>
  <nav-bar :title="t('service.title')" :show-bell="false" />
  <div class="content">
    <iframe 
      src="/service.html" 
      frameborder="0" 
      width="100%" 
      height="100%"
    ></iframe>
  </div>
</template>

<script setup>
import NavBar from '@/components/NavBar.vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

// 设置页面标题
</script>

<style scoped>
.content {
  padding: 0;
  height: calc(100vh - 46px);
  width: 100%;
  overflow: hidden;
}
</style> 