<template>
  <div class="content no-margin account-page">
    <div class="banner">
      <img src="@/assets/image/account/banner.png" alt="Facebook广告banner" />
      <div class="banner-title">{{ t('adCenter') }}</div>
    </div>

    <div class="welcome-container">
      <h1>{{ t('account.welcome') }}</h1>

      <div class="logo-container">
        <img src="@/assets/image/account/logo.png" alt="Facebook Logo" class="facebook-logo" />
      </div>

      <h2>{{ t('account.adNetwork') }}</h2>

      <p class="slogan">{{ t('account.slogan') }}</p>

      <div class="features">
        <div class="feature-item">
          <div class="feature-icon">
            <img src="@/assets/image/account/logo.png" alt="免费开户" />
          </div>
          <div class="feature-text">
            <h3>{{ t('account.freeAccount') }}</h3>
            <p>{{ t('account.freeAccountDesc') }}</p>
          </div>
        </div>

        <div class="feature-item">
          <div class="feature-icon">
            <img src="@/assets/image/account/logo.png" alt="产品运营服务" />
          </div>
          <div class="feature-text">
            <h3>{{ t('account.productService') }}</h3>
            <p>{{ t('account.productServiceDesc') }}</p>
          </div>
        </div>

        <div class="feature-item">
          <div class="feature-icon">
            <img src="@/assets/image/account/logo.png" alt="智能广告投放" />
          </div>
          <div class="feature-text">
            <h3>{{ t('account.smartAd') }}</h3>
            <p>{{ t('account.smartAdDesc') }}</p>
          </div>
        </div>
      </div>

      <div class="btn-container">
        <button class="create-account-btn" @click="showActionSheet">{{ t('account.createAccount') }}</button>
      </div>
    </div>

    <!-- ActionSheet 组件 -->
    <van-action-sheet v-model:show="actionSheetVisible" @cancel="onCancel" close-on-click-overlay>
      <div class="auth-container">
        <div class="auth-title">{{ t('account.createAccount') }}</div>
        <img src="@/assets/image/account/tips.png" alt="开户提示" class="tips-image" />
        <div class="auth-input-label">{{ t('account.enterAuthCode') }}</div>
        <input type="text" class="auth-input" v-model="authCode" placeholder="" />
        <button class="start-now-btn" @click="startNow" :disabled="!authCode.trim()"
          :class="{ 'btn-disabled': !authCode.trim(), 'btn-active': authCode.trim() }">
          {{ t('account.startNow') }}
        </button>
      </div>
    </van-action-sheet>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import { useTitle } from '@/utils/useTitle'
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { createAdAccount } from '@/api/adCenter'
import { showFailToast } from 'vant'
import { getUserInfo } from '@/utils/useUserInfo'
const { t } = useI18n()
const route = useRoute()
const router = useRouter()

// 设置页面标题
useTitle(() => t('accountTitle'))

// ActionSheet 相关
const actionSheetVisible = ref(false)
const authCode = ref('')


// 获取URL中的data参数
const url = route.query.url


// 显示 ActionSheet
const showActionSheet = () => {
  actionSheetVisible.value = true
}

// 取消时关闭 ActionSheet
const onCancel = () => {
  actionSheetVisible.value = false
}

// 开始按钮点击处理
const startNow = async () => {
  if (authCode.value.trim()) {
    try {
      // 调用创建广告账户接口
      await createAdAccount(authCode.value);
      await getUserInfo(true)
      // 接口调用成功，跳转到url参数指定的页面
      if (url) {
        router.push(`/${url}`);
      }
    } catch (error) {
      // 显示错误信息
      if (error.message) {
        showFailToast(error.message);
      } else {
        showFailToast('创建账户失败');
      }
      console.error('请求出错:', error);
    }
  }
}
</script>

<style scoped>

.banner {
  width: 100%;
  height: auto;
  overflow: hidden;
  position: relative;
}

.banner img {
  width: 100%;
  display: block;
}

.banner-title {
  position: absolute;
  top: 10%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 0.16rem;
  font-weight: bold;
  text-shadow: 0 0 0.1rem rgba(0, 0, 0, 0.5);
  z-index: 10;
}

.welcome-container {
  padding: 0.1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  height: calc(100% - 2rem);
  /* 减去banner图的大致高度 */
}

h1 {
  font-size: 0.18rem;
  margin-bottom: 0.05rem;
  margin-top: 0.05rem;
  font-weight: bold;
}

.logo-container {
  width: 0.4rem;
  height: 0.4rem;
  margin: 0.05rem 0;
}

.facebook-logo {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

h2 {
  font-size: 0.16rem;
  margin-bottom: 0.05rem;
  margin-top: 0.02rem;
  font-weight: bold;
}

.slogan {
  font-size: 0.12rem;
  color: #666;
  margin-bottom: 0.1rem;
  margin-top: 0.05rem;
}

.features {
  width: 100%;
  margin-top: 0.1rem;
  margin-bottom: 0.1rem;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 0.16rem;
  text-align: left;
}

.feature-icon {
  width: 0.25rem;
  height: 0.25rem;
  margin: 0 0.1rem;
  flex-shrink: 0;
}

.feature-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.feature-text {
  flex: 1;
}

.feature-text h3 {
  font-size: 0.14rem;
  margin: 0 0 0.02rem 0;
  font-weight: bold;
}

.feature-text p {
  font-size: 0.12rem;
  color: #666;
  margin: 0;
}

.btn-container {
  width: 100%;
  padding: 0.05rem 0;
  margin-top: 0.05rem;
}

.create-account-btn {
  width: 100%;
  height: 0.4rem;
  background-color: #0866ff;
  color: white;
  border: none;
  border-radius: 0.05rem;
  font-size: 0.16rem;
  font-weight: bold;
  cursor: pointer;
}

/* ActionSheet 自定义样式 */
.auth-container {
  padding: 0.3rem 0.15rem;
  text-align: center;
}

.auth-title {
  font-size: 0.18rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.2rem;
}

.tips-image {
  width: 100%;
  max-width: 3rem;
  margin: 0 auto 0.2rem;
  display: block;
}

.auth-input-label {
  font-size: 0.14rem;
  color: #333;
  margin-bottom: 0.1rem;
  text-align: left;
}

.auth-input {
  width: 100%;
  height: 0.4rem;
  border: 1px solid #ddd;
  border-radius: 0.05rem;
  padding: 0 0.1rem;
  margin-bottom: 0.2rem;
  font-size: 0.14rem;
  box-sizing: border-box;
}

.start-now-btn {
  width: 100%;
  height: 0.4rem;
  border: none;
  border-radius: 0.05rem;
  font-size: 0.16rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s;
}

.btn-disabled {
  background-color: #eee;
  color: #999;
  cursor: not-allowed;
}

.btn-active {
  background-color: #0866ff;
  color: white;
}
</style>