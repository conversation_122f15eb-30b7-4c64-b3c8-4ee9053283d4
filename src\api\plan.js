/**
 * 计划相关的API请求
 */
import request from '@/utils/request'

/**
 * 获取计划列表
 * @param {Object} params 查询参数
 * @param {string} [params.status] 计划状态：待投放, 匹配中, 投放中, 投放失败, 投放完成
 * @param {number} [params.page=1] 页码
 * @param {number} [params.pageSize=10] 每页条数
 * @returns {Promise}
 */
export function getPlanList(params) {
  return request({
    url: '/api/plan/list',
    method: 'get',
    params
  })
}

/**
 * 获取计划详情
 * @param {string} id 计划ID
 * @returns {Promise}
 */
export function getPlanDetail(id) {
  return request({
    url: `/api/plan/detail/${id}`,
    method: 'get'
  })
}

/**
 * 获取产品详情
 * @param {string} id 产品ID
 * @returns {Promise}
 */
export function getProductDetail(id) {
  return request({
    url: `/api/product/detail/${id}`,
    method: 'get'
  })
}

/**
 * 投放计划
 * @param {String} planId 计划ID
 * @param {Object} data 投放数据
 * @param {Number} data.amount 投放金额，单位为美元
 * @returns {Promise}
 */
export function launchPlan(planId, data) {
  return request({
    url: `/api/plan/launch/${planId}`,
    method: 'post',
    data
  })
}