<template>
  <!-- TikTok风格钱包页面 -->
  <div class="tiktok-wallet-container">
    <!-- 顶部导航 -->
    <div class="tiktok-header">
      <div class="header-left">
        <van-icon name="arrow-left" class="back-icon" @click="goBack" />
      </div>
      <div class="header-center">
        <span class="page-title">{{ t('wallet.title') }}</span>
      </div>
      <div class="header-right">
        <van-icon name="ellipsis" class="menu-icon" />
      </div>
    </div>

    <!-- 下拉刷新 -->
    <van-pull-refresh 
      v-model="loading" 
      @refresh="onRefresh" 
      :loading-text="t('refreshing')"
      :pull-text="t('pullToRefresh')"
      class="tiktok-refresh"
    >
      <div class="tiktok-content">
        <!-- TikTok风格资产总览 -->
        <div class="tiktok-asset-overview">
          <div class="asset-background">
            <div class="asset-glow"></div>
            <div class="floating-particles">
              <div class="particle" v-for="i in 6" :key="i"></div>
            </div>
          </div>
          
          <div class="asset-content">
            <!-- 总资产标题 -->
            <div class="asset-header">
              <div class="asset-title">
                <span>{{ t('wallet.totalAssets') }}</span>
                <van-icon 
                  :name="showBalance ? 'eye-o' : 'closed-eye'" 
                  @click="toggleBalanceVisibility" 
                  class="eye-icon" 
                />
              </div>
              <div class="currency-badge">USD</div>
            </div>

            <!-- 主要金额显示 -->
            <div class="main-amount-section">
              <template v-if="isLoading">
                <div class="amount-skeleton">
                  <div class="skeleton-amount"></div>
                </div>
              </template>
              <template v-else>
                <div class="main-amount">
                  {{ showBalance ? `$${formatAmount(walletData.totalAssets)}` : '******' }}
                </div>
              </template>
            </div>

            <!-- 余额详情网格 -->
            <div class="balance-grid">
              <template v-if="isLoading">
                <div class="balance-card skeleton-card" v-for="i in 4" :key="i">
                  <div class="skeleton-line"></div>
                  <div class="skeleton-line short"></div>
                </div>
              </template>
              <template v-else>
                <div class="balance-card available-card">
                  <div class="card-icon">
                    <van-icon name="balance-o" />
                  </div>
                  <div class="card-content">
                    <div class="card-label">{{ t('wallet.availableBalance') }}</div>
                    <div class="card-value">{{ showBalance ? `$${formatAmount(walletData.availableBalance)}` : '******' }}</div>
                  </div>
                  <div class="card-glow"></div>
                </div>

                <div class="balance-card pending-card">
                  <div class="card-icon">
                    <van-icon name="clock-o" />
                  </div>
                  <div class="card-content">
                    <div class="card-label">{{ t('wallet.pendingConsumption') }}</div>
                    <div class="card-value">{{ showBalance ? `$${formatAmount(walletData.pendingConsumption)}` : '******' }}</div>
                  </div>
                  <div class="card-glow"></div>
                </div>

                <div class="balance-card settlement-card">
                  <div class="card-icon">
                    <van-icon name="gold-coin-o" />
                  </div>
                  <div class="card-content">
                    <div class="card-label">{{ t('wallet.pendingSettlement') }}</div>
                    <div class="card-value">{{ showBalance ? `$${formatAmount(walletData.pendingSettlement)}` : '******' }}</div>
                  </div>
                  <div class="card-glow"></div>
                </div>

                <div class="balance-card withdraw-card">
                  <div class="card-icon">
                    <van-icon name="cash-back-record" />
                  </div>
                  <div class="card-content">
                    <div class="card-label">{{ t('wallet.withdrawing') }}</div>
                    <div class="card-value">{{ showBalance ? `$${formatAmount(walletData.pendingWithdrawal)}` : '******' }}</div>
                  </div>
                  <div class="card-glow"></div>
                </div>
              </template>
            </div>

            <!-- 操作按钮 -->
            <div class="action-buttons">
              <div class="tiktok-action-btn withdraw-btn" @click="showWithdrawSheet">
                <div class="btn-background"></div>
                <div class="btn-content">
                  <van-icon name="cash-back-record" class="btn-icon" />
                  <span class="btn-text">{{ t('wallet.withdraw') }}</span>
                </div>
                <div class="btn-glow"></div>
              </div>
              
              <div class="tiktok-action-btn deposit-btn" @click="showRechargeSheet">
                <div class="btn-background"></div>
                <div class="btn-content">
                  <van-icon name="gold-coin-o" class="btn-icon" />
                  <span class="btn-text">{{ t('wallet.deposit') }}</span>
                </div>
                <div class="btn-glow"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- TikTok风格钱包菜单 -->
        <div class="tiktok-wallet-menu">
          <div class="menu-title">钱包设置</div>
          <div class="menu-grid">
            <div class="tiktok-menu-item" @click="goToBankCard">
              <div class="menu-item-background"></div>
              <div class="menu-item-content">
                <div class="menu-icon-wrapper">
                  <van-icon name="card" class="menu-icon" />
                  <div class="icon-pulse"></div>
                </div>
                <div class="menu-text">{{ t('wallet.bindBankCard') }}</div>
                <van-icon name="arrow" class="arrow-icon" />
              </div>
            </div>

            <div class="tiktok-menu-item" @click="goToCrypto">
              <div class="menu-item-background"></div>
              <div class="menu-item-content">
                <div class="menu-icon-wrapper">
                  <van-icon name="gold-coin" class="menu-icon" />
                  <div class="icon-pulse"></div>
                </div>
                <div class="menu-text">{{ t('wallet.bindCrypto') }}</div>
                <van-icon name="arrow" class="arrow-icon" />
              </div>
            </div>

            <div class="tiktok-menu-item" @click="goToSecuritySettings">
              <div class="menu-item-background"></div>
              <div class="menu-item-content">
                <div class="menu-icon-wrapper">
                  <van-icon name="lock" class="menu-icon" />
                  <div class="icon-pulse"></div>
                </div>
                <div class="menu-text">{{ t('wallet.transactionPasswordSettings') }}</div>
                <van-icon name="arrow" class="arrow-icon" />
              </div>
            </div>
          </div>
        </div>

        <!-- TikTok风格交易记录 -->
        <div class="tiktok-transactions">
          <div class="transactions-header">
            <h3 class="transactions-title">{{ t('wallet.fundDetails') }}</h3>
            <div class="title-glow"></div>
          </div>

          <div class="transactions-content">
            <template v-if="isTransactionsLoading">
              <div class="transaction-skeleton" v-for="i in 3" :key="i">
                <div class="skeleton-avatar"></div>
                <div class="skeleton-content">
                  <div class="skeleton-line"></div>
                  <div class="skeleton-line short"></div>
                </div>
              </div>
            </template>
            <template v-else>
              <van-list 
                v-model:loading="listLoading" 
                :finished="finished" 
                @load="onLoad"
                :finished-text="t('noMoreData')" 
                :loading-text="t('loading')"
              >
                <div 
                  class="tiktok-transaction-item" 
                  v-for="item in transactions" 
                  :key="item.id"
                  @click="showTransactionDetail(item)"
                >
                  <div class="transaction-background"></div>
                  <div class="transaction-content">
                    <div class="transaction-icon-wrapper">
                      <van-icon :name="getTransactionIcon(item.type)" class="transaction-icon" />
                      <div class="transaction-icon-glow"></div>
                    </div>
                    <div class="transaction-info">
                      <div class="transaction-type">{{ getTransactionTypeName(item.type) }}</div>
                      <div class="transaction-date">{{ item.create_time }}</div>
                    </div>
                    <div class="transaction-amount-wrapper">
                      <div class="transaction-amount" :class="getAmountClass(item.amount)">
                        {{ formatTransactionAmount(item.amount) }}
                      </div>
                      <van-icon name="arrow" class="transaction-arrow" />
                    </div>
                  </div>
                  <div class="item-glow"></div>
                </div>
              </van-list>
            </template>
          </div>
        </div>
      </div>
    </van-pull-refresh>
  </div>

  <!-- 提现弹窗 -->
  <van-action-sheet v-model:show="withdrawVisible" :close-on-click-overlay="true" class="tiktok-sheet">
    <div class="tiktok-withdraw-container">
      <div class="withdraw-header">
        <div class="withdraw-title">{{ t('wallet.withdraw') }}</div>
        <div class="withdraw-icon">
          <van-icon name="cash-back-record" />
        </div>
      </div>
      
      <div class="withdraw-form">
        <div class="amount-input-section">
          <div class="input-label">{{ t('wallet.withdrawAmount') }}</div>
          <div class="amount-input-wrapper">
            <span class="currency-symbol">$</span>
            <input 
              type="number" 
              v-model="withdrawAmount" 
              class="amount-input"
              :placeholder="t('wallet.withdrawAmount')" 
            />
          </div>
          <div class="available-info">
            <span>{{ t('wallet.availableBalance') }} ${{ formatAmount(walletData.availableBalance) }}</span>
            <span class="max-withdraw" @click="setMaxWithdraw">{{ t('wallet.allWithdraw') }}</span>
          </div>
        </div>

        <div class="withdraw-type-section">
          <van-radio-group v-model="withdrawType" direction="horizontal" class="type-radio-group">
            <div class="type-title">{{ t('wallet.withdrawType') }}</div>
            <van-radio :disabled="!isSetBank" name="0" class="type-radio">{{ t('wallet.bankCard') }}</van-radio>
            <van-radio :disabled="!isSetCrypto" name="1" class="type-radio">{{ t('wallet.crypto') }}</van-radio>
          </van-radio-group>
        </div>

        <div class="withdraw-action">
          <div class="tiktok-withdraw-btn" @click="validateWithdraw">
            <div class="withdraw-btn-background"></div>
            <div class="withdraw-btn-content">
              <span>{{ t('wallet.withdraw') }}</span>
            </div>
            <div class="withdraw-btn-glow"></div>
          </div>
        </div>
      </div>
    </div>
  </van-action-sheet>

  <!-- 存款弹窗 -->
  <van-action-sheet v-model:show="rechargeVisible" :close-on-click-overlay="true" class="tiktok-sheet">
    <div class="tiktok-recharge-container">
      <div class="recharge-header">
        <div class="recharge-title">{{ t('wallet.depositTips') }}</div>
        <div class="recharge-icon">
          <van-icon name="gold-coin-o" />
        </div>
      </div>
      
      <div class="recharge-banner">
        <img src="@/assets/image/adcenter/wallet/recharge-banner.png" alt="存款提示" />
      </div>
      
      <div class="recharge-content">
        <p>{{ t('wallet.currencyExchangeIssue') }}</p>
        <p>{{ t('wallet.manualDepositOnly') }}</p>
        <p>{{ t('wallet.contactCustomerService') }}</p>
      </div>
      
      <div class="recharge-action">
        <div class="tiktok-contact-btn" @click="contactCustomerService">
          <div class="contact-btn-background"></div>
          <div class="contact-btn-content">
            <span>{{ t('wallet.contactService') }}</span>
          </div>
          <div class="contact-btn-glow"></div>
        </div>
      </div>
    </div>
  </van-action-sheet>

  <!-- 交易详情弹窗 -->
  <van-action-sheet v-model:show="transactionDetailVisible" :close-on-click-overlay="true" class="tiktok-sheet">
    <div class="tiktok-transaction-detail">
      <div class="detail-header">
        <div class="detail-title">{{ t('wallet.detailTitle') }}</div>
        <div class="detail-icon">
          <van-icon name="description" />
        </div>
      </div>
      
      <div class="detail-content">
        <div class="detail-item">
          <div class="detail-label">{{ t('wallet.detailId') }}</div>
          <div class="detail-value">{{ currentTransaction.transaction_code }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">{{ t('wallet.detailTime') }}</div>
          <div class="detail-value">{{ currentTransaction.create_time }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">{{ t('wallet.detailAmount') }}</div>
          <div class="detail-value" :class="getAmountClass(currentTransaction.amount)">
            {{ formatTransactionAmount(currentTransaction.amount) }}
          </div>
        </div>
        <div class="detail-item">
          <div class="detail-label">{{ t('wallet.balanceBefore') }}</div>
          <div class="detail-value">${{ currentTransaction.balance_before }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">{{ t('wallet.balanceAfter') }}</div>
          <div class="detail-value">${{ currentTransaction.balance_after }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">{{ t('wallet.status') }}</div>
          <div class="detail-value">{{ currentTransaction.statusText || currentTransaction.status }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">{{ t('wallet.detailRemark') }}</div>
          <div class="detail-value">{{ currentTransaction.remark || t('wallet.noRemark') }}</div>
        </div>
      </div>
    </div>
  </van-action-sheet>

  <!-- 密码问题弹窗 -->
  <van-action-sheet v-model:show="passwordContactVisible" :close-on-click-overlay="true" class="tiktok-sheet">
    <div class="tiktok-password-contact">
      <div class="password-header">
        <div class="password-title">{{ t('wallet.passwordContactTitle') }}</div>
        <div class="password-icon">
          <van-icon name="lock" />
        </div>
      </div>
      
      <div class="password-banner">
        <img src="@/assets/image/adcenter/wallet/pwdContact.png" alt="密码问题" />
      </div>
      
      <div class="password-content">
        <p>{{ t('wallet.passwordContactContent1') }}</p>
        <p>{{ t('wallet.passwordContactContent2') }}</p>
      </div>
      
      <div class="password-action">
        <div class="tiktok-contact-btn" @click="contactCustomerService">
          <div class="contact-btn-background"></div>
          <div class="contact-btn-content">
            <span>{{ t('wallet.contactService') }}</span>
          </div>
          <div class="contact-btn-glow"></div>
        </div>
      </div>
    </div>
  </van-action-sheet>

  <!-- 交易密码验证组件 -->
  <transaction-password-verify
    v-model:show="showPasswordVerify"
    @confirm="handlePasswordConfirm"
    @cancel="handlePasswordCancel"
  />
</template>

<script setup>
import { useTitle } from '@/utils/useTitle'
import { Icon as VanIcon, Empty as VanEmpty } from 'vant'
import { ActionSheet as VanActionSheet, PullRefresh as VanPullRefresh, List as VanList } from 'vant'
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { walletApi } from '@/api'
import { showToast, Radio as VanRadio, RadioGroup as VanRadioGroup } from 'vant'
import { useUserStore } from '@/stores/user'
import TransactionPasswordVerify from '@/components/TransactionPasswordVerify.vue'

const router = useRouter()
const { t } = useI18n()
const userStore = useUserStore()

// 设置页面标题
useTitle(() => t('wallet.title'))

// 返回上一页
const goBack = () => {
  router.back()
}

const withdrawType = ref('bankCard')
const isSetBank = ref(0)
const isSetCrypto = ref(0)
isSetBank.value = userStore.userInfo.isSetBank
isSetCrypto.value = userStore.userInfo.isSetCrypto

// 钱包数据
const walletData = ref({
  totalAssets: 0,
  availableBalance: 0,
  pendingConsumption: 0,
  pendingSettlement: 0,
  pendingWithdrawal: 0
})

// 交易记录
const transactions = ref([])
const currentTransaction = ref({})
const transactionDetailVisible = ref(false)

// 加载状态
const isLoading = ref(true)
const isTransactionsLoading = ref(true)
const loading = ref(false)
const listLoading = ref(false)
const finished = ref(false)
const currentPage = ref(1)
const pageSize = 10

// 弹窗状态
const withdrawVisible = ref(false)
const rechargeVisible = ref(false)
const passwordContactVisible = ref(false)
const showPasswordVerify = ref(false)
const withdrawAmount = ref('')

// 处理带逗号的金额字符串转为数字
const parseAmount = (amountStr) => {
  if (!amountStr) return 0
  if (typeof amountStr === 'number') return amountStr
  return parseFloat(amountStr.toString().replace(/,/g, '')) || 0
}

// 获取钱包信息
const fetchWalletInfo = async () => {
  isLoading.value = true
  try {
    const res = await walletApi.fetchWalletInfo()
    if (res.code === 200 && res.data) {
      walletData.value = {
        totalAssets: parseAmount(res.data.totalAssets),
        availableBalance: parseAmount(res.data.availableBalance),
        pendingConsumption: parseAmount(res.data.pendingConsumption),
        pendingSettlement: parseAmount(res.data.pendingSettlement),
        pendingWithdrawal: parseAmount(res.data.pendingWithdrawal)
      }
    } else {
      showToast(res.message)
    }
  } finally {
    isLoading.value = false
  }
}

// 获取交易记录
const fetchTransactions = async (page = 1, isRefresh = false) => {
  if (page === 1) {
    isTransactionsLoading.value = true
  }

  try {
    const res = await walletApi.fetchWalletTransactions({
      page,
      limit: pageSize
    })

    if (res.code === 200 && res.data) {
      if (isRefresh || page === 1) {
        transactions.value = res.data.items || []
      } else {
        transactions.value = [...transactions.value, ...(res.data.items || [])]
      }

      finished.value = !res.data.items || res.data.items.length < pageSize ||
        (res.data.page * pageSize) >= res.data.total
    }
  } finally {
    isTransactionsLoading.value = false
    listLoading.value = false
    loading.value = false
  }
}

// 下拉刷新
const onRefresh = async () => {
  currentPage.value = 1
  finished.value = false
  await Promise.all([
    fetchWalletInfo(),
    fetchTransactions(1, true)
  ])
}

// 加载更多
const onLoad = () => {
  currentPage.value += 1
  fetchTransactions(currentPage.value)
}

// 显示交易详情
const showTransactionDetail = (transaction) => {
  currentTransaction.value = transaction
  transactionDetailVisible.value = true
}

// 组件挂载时获取钱包信息和交易记录
onMounted(() => {
  fetchWalletInfo()
  fetchTransactions()
})

// 控制余额显示状态
const showBalance = ref(true)

// 切换余额显示/隐藏
const toggleBalanceVisibility = () => {
  showBalance.value = !showBalance.value
}

// 显示存款ActionSheet
const showRechargeSheet = () => {
  rechargeVisible.value = true
}

// 显示提现ActionSheet
const showWithdrawSheet = () => {
  if (!checkIsSetPassword()) {
    router.push('/adcenter/wallet/password')
  } else if (!checkIsSetBankCard()) {
    showToast(t('wallet.bindCardFirst'))
  } else {
    withdrawVisible.value = true
  }
}

// 验证提现信息并显示密码验证
const validateWithdraw = () => {
  if (!withdrawAmount.value || parseAmount(withdrawAmount.value) <= 0) {
    showToast(t('wallet.withdrawAmountInvalid'))
    return
  }

  if (parseAmount(withdrawAmount.value) > walletData.value.availableBalance) {
    showToast(t('wallet.withdrawAmountExceed'))
    return
  }

  showPasswordVerify.value = true
}

// 密码验证成功回调
const handlePasswordConfirm = async (password) => {
  try {
    const res = await walletApi.withdraw({
      amount: parseAmount(withdrawAmount.value),
      payPassword: password,
      type: withdrawType.value
    })

    if (res.code === 200) {
      showToast('提现申请已提交')
      showPasswordVerify.value = false
      withdrawVisible.value = false
      withdrawAmount.value = ''
      fetchWalletInfo()
    } else {
      showToast(res.message || '提现失败')
    }
  } catch (error) {
    showToast('提现失败，请稍后再试')
  }
}

// 密码验证取消回调
const handlePasswordCancel = () => {
  showPasswordVerify.value = false
}

// 设置最大提现金额
const setMaxWithdraw = () => {
  withdrawAmount.value = walletData.value.availableBalance.toString()
}

// 显示密码问题弹窗
const showPasswordContactSheet = () => {
  passwordContactVisible.value = true
}

// 联系客服
const contactCustomerService = () => {
  router.push('/adcenter/service')
  passwordContactVisible.value = false
  rechargeVisible.value = false
}

const checkIsSetPassword = () => {
  return userStore.userInfo.isSetPwd
}

const checkIsSetBankCard = () => {
  return userStore.userInfo.isSetBank || userStore.userInfo.isSetCrypto
}

// 绑定银行卡页面跳转
const goToBankCard = () => {
  if (!checkIsSetPassword()) {
    router.push('/adcenter/wallet/password')
  } else {
    router.push('/adcenter/wallet/bank')
  }
}

// 绑定虚拟货币页面跳转
const goToCrypto = () => {
  if (!checkIsSetPassword()) {
    router.push('/adcenter/wallet/password')
  } else {
    router.push('/adcenter/wallet/crypto')
  }
}

// 交易密码设置页面跳转
const goToSecuritySettings = () => {
  if (checkIsSetPassword() === 0) {
    router.push('/adcenter/wallet/password')
  } else {
    showPasswordContactSheet()
  }
}

// 格式化金额显示
const formatAmount = (amount) => {
  const num = parseAmount(amount)
  return num.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

// 格式化交易金额显示
const formatTransactionAmount = (amount) => {
  if (!amount) return '$0.00'

  if (typeof amount === 'string') {
    if (amount.includes('$')) {
      return amount
    }
    if (amount.startsWith('-')) {
      return `-$${amount.substring(1)}`
    } else {
      return `$${amount}`
    }
  }

  const num = parseAmount(amount)
  if (num > 0) {
    return `$${formatAmount(num)}`
  } else if (num < 0) {
    return `-$${formatAmount(Math.abs(num))}`
  }
  return '$0.00'
}

// 获取交易类型名称
const getTransactionTypeName = (type) => {
  const typeMap = {
    'DEPOSIT': t('wallet.systemDeposit'),
    'WITHDRAW': t('wallet.withdraw'),
    'AD_PLACEMENT': t('wallet.adPlacement'),
    'AD_SETTLEMENT': t('wallet.adSettlement'),
    'VIP_ACTIVATION': t('wallet.vipActivation'),
    '申请提现': t('wallet.withdraw'),
    '系统充值': t('wallet.systemDeposit'),
    '广告投放': t('wallet.adPlacement'),
    '广告结算': t('wallet.adSettlement'),
    'VIP激活': t('wallet.vipActivation'),
    'vip_upgrade': t('wallet.vipUpgrade'),
  }
  return typeMap[type] || type
}

// 获取交易图标
const getTransactionIcon = (type) => {
  const iconMap = {
    'DEPOSIT': 'gold-coin-o',
    'WITHDRAW': 'cash-back-record',
    'AD_PLACEMENT': 'chart-trending-o',
    'AD_SETTLEMENT': 'balance-o',
    'VIP_ACTIVATION': 'diamond-o',
    '申请提现': 'cash-back-record',
    '系统充值': 'gold-coin-o',
    '广告投放': 'chart-trending-o',
    '广告结算': 'balance-o',
    'VIP激活': 'diamond-o',
    'vip_upgrade': 'diamond-o',
  }
  return iconMap[type] || 'description'
}

// 获取金额类名
const getAmountClass = (amount) => {
  if (typeof amount === 'string') {
    if (amount.startsWith('-')) {
      return 'negative-amount'
    } else if (amount !== '0' && amount !== '0.00') {
      return 'positive-amount'
    }
    return ''
  }

  const num = parseFloat(amount)
  if (num > 0) {
    return 'positive-amount'
  } else if (num < 0) {
    return 'negative-amount'
  }
  return ''
}
</script>

<style scoped>
/* TikTok风格钱包页面全局样式 */
.tiktok-wallet-container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(180deg, #000000 0%, #1a1a1a 30%, #0d4f3c 70%, #000000 100%);
  color: #ffffff;
  overflow-x: hidden;
}

/* TikTok风格顶部导航 */
.tiktok-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 0.6rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 0.16rem;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 1000;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-left,
.header-right {
  width: 0.4rem;
  display: flex;
  justify-content: center;
}

.header-center {
  flex: 1;
  text-align: center;
}

.back-icon,
.menu-icon {
  font-size: 0.24rem;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-icon:active,
.menu-icon:active {
  transform: scale(0.9);
  color: #00ff88;
}

.page-title {
  font-size: 0.18rem;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

/* TikTok风格下拉刷新 */
.tiktok-refresh {
  padding-top: 0.6rem;
  min-height: 100vh;
}

.tiktok-content {
  padding: 0.2rem 0.16rem 0.4rem;
}

/* TikTok风格资产总览 */
.tiktok-asset-overview {
  position: relative;
  margin-bottom: 0.24rem;
  border-radius: 0.24rem;
  overflow: hidden;
  background: linear-gradient(135deg, #00ff88 0%, #00d4aa 50%, #0099cc 100%);
  box-shadow: 0 12px 40px rgba(0, 255, 136, 0.4);
  min-height: 3rem;
}

.asset-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 255, 136, 0.9) 0%, rgba(0, 153, 204, 0.9) 100%);
}

.asset-glow {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
  animation: assetGlow 4s ease-in-out infinite;
}

@keyframes assetGlow {
  0%, 100% { transform: rotate(0deg) scale(1); opacity: 0.3; }
  50% { transform: rotate(180deg) scale(1.1); opacity: 0.6; }
}

.floating-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.particle:nth-child(1) { top: 20%; left: 10%; animation-delay: 0s; }
.particle:nth-child(2) { top: 60%; left: 20%; animation-delay: 1s; }
.particle:nth-child(3) { top: 30%; left: 70%; animation-delay: 2s; }
.particle:nth-child(4) { top: 80%; left: 80%; animation-delay: 3s; }
.particle:nth-child(5) { top: 10%; left: 50%; animation-delay: 4s; }
.particle:nth-child(6) { top: 70%; left: 40%; animation-delay: 5s; }

@keyframes float {
  0%, 100% { transform: translateY(0px) scale(1); opacity: 0.6; }
  50% { transform: translateY(-20px) scale(1.2); opacity: 1; }
}

.asset-content {
  position: relative;
  z-index: 2;
  padding: 0.32rem;
}

.asset-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.2rem;
}

.asset-title {
  display: flex;
  align-items: center;
  gap: 0.08rem;
  font-size: 0.16rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
}

.eye-icon {
  font-size: 0.18rem;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s ease;
}

.eye-icon:active {
  transform: scale(0.9);
  color: #ffffff;
}

.currency-badge {
  background: rgba(0, 0, 0, 0.3);
  color: #ffffff;
  padding: 0.04rem 0.12rem;
  border-radius: 0.12rem;
  font-size: 0.12rem;
  font-weight: 600;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.main-amount-section {
  margin-bottom: 0.24rem;
  min-height: 0.4rem;
  display: flex;
  align-items: center;
}

.amount-skeleton {
  width: 100%;
}

.skeleton-amount {
  width: 60%;
  height: 0.32rem;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 0.08rem;
  animation: skeletonPulse 1.5s ease-in-out infinite;
}

@keyframes skeletonPulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.6; }
}

.main-amount {
  font-size: 0.36rem;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.02rem;
}

/* 余额网格 */
.balance-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.12rem;
  margin-bottom: 0.24rem;
}

.balance-card {
  position: relative;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 0.16rem;
  padding: 0.16rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  transition: all 0.3s ease;
}

.balance-card:active {
  transform: scale(0.98);
}

.skeleton-card {
  background: rgba(255, 255, 255, 0.1);
}

.skeleton-line {
  height: 0.12rem;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 0.06rem;
  margin-bottom: 0.08rem;
  animation: skeletonPulse 1.5s ease-in-out infinite;
}

.skeleton-line.short {
  width: 60%;
}

.available-card {
  border-color: rgba(0, 255, 136, 0.4);
}

.pending-card {
  border-color: rgba(255, 193, 7, 0.4);
}

.settlement-card {
  border-color: rgba(255, 87, 34, 0.4);
}

.withdraw-card {
  border-color: rgba(156, 39, 176, 0.4);
}

.card-icon {
  width: 0.32rem;
  height: 0.32rem;
  border-radius: 0.08rem;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.12rem;
  font-size: 0.16rem;
  color: #ffffff;
}

.card-content {
  flex: 1;
}

.card-label {
  font-size: 0.1rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 0.04rem;
  line-height: 1.2;
}

.card-value {
  font-size: 0.14rem;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.card-glow {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 0.18rem;
  opacity: 0;
  z-index: -1;
  transition: opacity 0.3s ease;
}

.available-card .card-glow {
  background: linear-gradient(45deg, rgba(0, 255, 136, 0.3), rgba(0, 212, 170, 0.3));
}

.pending-card .card-glow {
  background: linear-gradient(45deg, rgba(255, 193, 7, 0.3), rgba(255, 235, 59, 0.3));
}

.settlement-card .card-glow {
  background: linear-gradient(45deg, rgba(255, 87, 34, 0.3), rgba(255, 152, 0, 0.3));
}

.withdraw-card .card-glow {
  background: linear-gradient(45deg, rgba(156, 39, 176, 0.3), rgba(233, 30, 99, 0.3));
}

.balance-card:hover .card-glow {
  opacity: 1;
  animation: cardGlow 2s ease-in-out infinite;
}

@keyframes cardGlow {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.6; }
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 0.16rem;
}

.tiktok-action-btn {
  position: relative;
  flex: 1;
  height: 0.48rem;
  border-radius: 0.24rem;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tiktok-action-btn:active {
  transform: scale(0.98);
}

.btn-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  transition: all 0.3s ease;
}

.withdraw-btn .btn-background {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.deposit-btn .btn-background {
  background: linear-gradient(135deg, #00ff88 0%, #00d4aa 100%);
}

.btn-content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  gap: 0.08rem;
}

.btn-icon {
  font-size: 0.16rem;
  color: #ffffff;
}

.btn-text {
  font-size: 0.14rem;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.deposit-btn .btn-text {
  color: #000;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
}

.deposit-btn .btn-icon {
  color: #000;
}

.btn-glow {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 0.26rem;
  opacity: 0;
  z-index: -1;
  transition: opacity 0.3s ease;
}

.withdraw-btn .btn-glow {
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.2));
}

.deposit-btn .btn-glow {
  background: linear-gradient(45deg, rgba(0, 255, 136, 0.6), rgba(0, 212, 170, 0.6));
}

.tiktok-action-btn:hover .btn-glow {
  opacity: 1;
  animation: btnGlow 2s ease-in-out infinite;
}

@keyframes btnGlow {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

/* TikTok风格钱包菜单 */
.tiktok-wallet-menu {
  margin-bottom: 0.24rem;
}

.menu-title {
  font-size: 0.18rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 0.16rem;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.menu-grid {
  display: flex;
  flex-direction: column;
  gap: 0.12rem;
}

.tiktok-menu-item {
  position: relative;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-radius: 0.16rem;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
}

.tiktok-menu-item:active {
  transform: scale(0.98);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%);
}

.menu-item-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(0, 255, 136, 0.1), rgba(0, 212, 170, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.tiktok-menu-item:active .menu-item-background {
  opacity: 1;
}

.menu-item-content {
  position: relative;
  z-index: 2;
  padding: 0.16rem;
  display: flex;
  align-items: center;
}

.menu-icon-wrapper {
  position: relative;
  width: 0.4rem;
  height: 0.4rem;
  border-radius: 0.12rem;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.16rem;
}

.menu-icon {
  font-size: 0.2rem;
  color: #00ff88;
}

.icon-pulse {
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border: 2px solid rgba(0, 255, 136, 0.3);
  border-radius: 0.16rem;
  animation: iconPulse 2s ease-in-out infinite;
}

@keyframes iconPulse {
  0%, 100% { opacity: 0.6; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.05); }
}

.menu-text {
  flex: 1;
  font-size: 0.14rem;
  font-weight: 500;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.arrow-icon {
  font-size: 0.16rem;
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.tiktok-menu-item:active .arrow-icon {
  color: #00ff88;
  transform: translateX(2px);
}

/* TikTok风格交易记录 */
.tiktok-transactions {
  margin-bottom: 0.24rem;
}

.transactions-header {
  position: relative;
  margin-bottom: 0.16rem;
}

.transactions-title {
  font-size: 0.18rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.title-glow {
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 0.6rem;
  height: 2px;
  background: linear-gradient(90deg, #00ff88, transparent);
  border-radius: 1px;
}

.transactions-content {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 0.16rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

.transaction-skeleton {
  display: flex;
  align-items: center;
  padding: 0.16rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.transaction-skeleton:last-child {
  border-bottom: none;
}

.skeleton-avatar {
  width: 0.4rem;
  height: 0.4rem;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  margin-right: 0.12rem;
  animation: skeletonPulse 1.5s ease-in-out infinite;
}

.skeleton-content {
  flex: 1;
}

.tiktok-transaction-item {
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.tiktok-transaction-item:last-child {
  border-bottom: none;
}

.tiktok-transaction-item:active {
  background: rgba(255, 255, 255, 0.1);
}

.transaction-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(0, 255, 136, 0.05), rgba(0, 212, 170, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.tiktok-transaction-item:active .transaction-background {
  opacity: 1;
}

.transaction-content {
  position: relative;
  z-index: 2;
  padding: 0.16rem;
  display: flex;
  align-items: center;
}

.transaction-icon-wrapper {
  position: relative;
  width: 0.4rem;
  height: 0.4rem;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.12rem;
}

.transaction-icon {
  font-size: 0.18rem;
  color: #00ff88;
}

.transaction-icon-glow {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(0, 255, 136, 0.3), rgba(0, 212, 170, 0.3));
  opacity: 0;
  z-index: -1;
  transition: opacity 0.3s ease;
}

.tiktok-transaction-item:hover .transaction-icon-glow {
  opacity: 1;
  animation: iconGlow 2s ease-in-out infinite;
}

@keyframes iconGlow {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.6; }
}

.transaction-info {
  flex: 1;
}

.transaction-type {
  font-size: 0.14rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 0.04rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.transaction-date {
  font-size: 0.12rem;
  color: rgba(255, 255, 255, 0.7);
}

.transaction-amount-wrapper {
  display: flex;
  align-items: center;
  gap: 0.08rem;
}

.transaction-amount {
  font-size: 0.14rem;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.positive-amount {
  color: #00ff88;
}

.negative-amount {
  color: #ff4757;
}

.transaction-arrow {
  font-size: 0.14rem;
  color: rgba(255, 255, 255, 0.6);
  transition: all 0.3s ease;
}

.tiktok-transaction-item:active .transaction-arrow {
  color: #00ff88;
  transform: translateX(2px);
}

.item-glow {
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: linear-gradient(45deg, rgba(0, 255, 136, 0.2), rgba(0, 212, 170, 0.2));
  opacity: 0;
  z-index: -1;
  transition: opacity 0.3s ease;
}

.tiktok-transaction-item:hover .item-glow {
  opacity: 1;
}

/* TikTok风格弹窗样式 */
.tiktok-sheet :deep(.van-action-sheet) {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.95) 0%, rgba(26, 26, 26, 0.95) 100%);
  border-top-left-radius: 0.2rem;
  border-top-right-radius: 0.2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
}

.tiktok-sheet :deep(.van-action-sheet__header) {
  background: transparent;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.tiktok-sheet :deep(.van-action-sheet__content) {
  background: transparent;
}

/* 提现弹窗样式 */
.tiktok-withdraw-container {
  padding: 0.24rem;
  color: #ffffff;
}

.withdraw-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.24rem;
  gap: 0.12rem;
}

.withdraw-title {
  font-size: 0.2rem;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.withdraw-icon {
  font-size: 0.24rem;
  color: #00ff88;
  text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
}

.withdraw-form {
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
}

.amount-input-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0.16rem;
  padding: 0.2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.input-label {
  font-size: 0.14rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0.12rem;
}

.amount-input-wrapper {
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 0.12rem;
  padding: 0.12rem 0.16rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 0.12rem;
}

.currency-symbol {
  font-size: 0.18rem;
  font-weight: 700;
  color: #00ff88;
  margin-right: 0.08rem;
}

.amount-input {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  font-size: 0.16rem;
  color: #ffffff;
  font-weight: 600;
}

.amount-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.available-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.12rem;
  color: rgba(255, 255, 255, 0.7);
}

.max-withdraw {
  color: #00ff88;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.max-withdraw:active {
  transform: scale(0.95);
  color: #00d4aa;
}

.withdraw-type-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0.16rem;
  padding: 0.2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.type-radio-group {
  display: flex;
  flex-direction: column;
  gap: 0.12rem;
}

.type-title {
  font-size: 0.14rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0.12rem;
}

.type-radio :deep(.van-radio__label) {
  color: #ffffff;
  font-size: 0.14rem;
}

.type-radio :deep(.van-radio__icon--checked) {
  background-color: #00ff88;
  border-color: #00ff88;
}

.withdraw-action {
  margin-top: 0.16rem;
}

.tiktok-withdraw-btn {
  position: relative;
  width: 100%;
  height: 0.48rem;
  border-radius: 0.24rem;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tiktok-withdraw-btn:active {
  transform: scale(0.98);
}

.withdraw-btn-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #00ff88 0%, #00d4aa 100%);
}

.withdraw-btn-content {
  position: relative;
  z-index: 2;
}

.withdraw-btn-content span {
  font-size: 0.16rem;
  font-weight: 600;
  color: #000;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
}

.withdraw-btn-glow {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, rgba(0, 255, 136, 0.6), rgba(0, 212, 170, 0.6));
  border-radius: 0.26rem;
  opacity: 0;
  z-index: -1;
  transition: opacity 0.3s ease;
}

.tiktok-withdraw-btn:hover .withdraw-btn-glow {
  opacity: 1;
  animation: withdrawGlow 2s ease-in-out infinite;
}

@keyframes withdrawGlow {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

/* 存款弹窗样式 */
.tiktok-recharge-container {
  padding: 0.24rem;
  color: #ffffff;
  text-align: center;
}

.recharge-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.24rem;
  gap: 0.12rem;
}

.recharge-title {
  font-size: 0.2rem;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.recharge-icon {
  font-size: 0.24rem;
  color: #ffd700;
  text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.recharge-banner {
  margin-bottom: 0.2rem;
}

.recharge-banner img {
  width: 100%;
  max-width: 2rem;
  height: auto;
  border-radius: 0.12rem;
}

.recharge-content {
  margin-bottom: 0.24rem;
}

.recharge-content p {
  font-size: 0.14rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0.08rem 0;
  line-height: 1.4;
}

.recharge-action {
  margin-top: 0.16rem;
}

.tiktok-contact-btn {
  position: relative;
  width: 100%;
  height: 0.48rem;
  border-radius: 0.24rem;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tiktok-contact-btn:active {
  transform: scale(0.98);
}

.contact-btn-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
}

.contact-btn-content {
  position: relative;
  z-index: 2;
}

.contact-btn-content span {
  font-size: 0.16rem;
  font-weight: 600;
  color: #000;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
}

.contact-btn-glow {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, rgba(255, 215, 0, 0.6), rgba(255, 237, 78, 0.6));
  border-radius: 0.26rem;
  opacity: 0;
  z-index: -1;
  transition: opacity 0.3s ease;
}

.tiktok-contact-btn:hover .contact-btn-glow {
  opacity: 1;
  animation: contactGlow 2s ease-in-out infinite;
}

@keyframes contactGlow {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

/* 交易详情弹窗样式 */
.tiktok-transaction-detail {
  padding: 0.24rem;
  color: #ffffff;
}

.detail-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.24rem;
  gap: 0.12rem;
}

.detail-title {
  font-size: 0.2rem;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.detail-icon {
  font-size: 0.24rem;
  color: #00ff88;
  text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 0.16rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.12rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 0.14rem;
  color: rgba(255, 255, 255, 0.7);
}

.detail-value {
  font-size: 0.14rem;
  font-weight: 600;
  color: #ffffff;
  text-align: right;
  max-width: 60%;
  word-break: break-all;
}

/* 密码问题弹窗样式 */
.tiktok-password-contact {
  padding: 0.24rem;
  color: #ffffff;
  text-align: center;
}

.password-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.24rem;
  gap: 0.12rem;
}

.password-title {
  font-size: 0.2rem;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.password-icon {
  font-size: 0.24rem;
  color: #ff4757;
  text-shadow: 0 0 10px rgba(255, 71, 87, 0.5);
}

.password-banner {
  margin-bottom: 0.2rem;
}

.password-banner img {
  width: 100%;
  max-width: 2rem;
  height: auto;
  border-radius: 0.12rem;
}

.password-content {
  margin-bottom: 0.24rem;
}

.password-content p {
  font-size: 0.14rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0.08rem 0;
  line-height: 1.4;
}

.password-action {
  margin-top: 0.16rem;
}

/* 下拉刷新和列表样式覆盖 */
.tiktok-refresh :deep(.van-pull-refresh__track) {
  background: transparent;
}

.tiktok-refresh :deep(.van-pull-refresh__head) {
  color: #ffffff;
}

.tiktok-refresh :deep(.van-list__finished-text) {
  color: rgba(255, 255, 255, 0.6);
}

.tiktok-refresh :deep(.van-list__loading-text) {
  color: rgba(255, 255, 255, 0.6);
}

.tiktok-refresh :deep(.van-loading__spinner) {
  color: #00ff88;
}
</style>
