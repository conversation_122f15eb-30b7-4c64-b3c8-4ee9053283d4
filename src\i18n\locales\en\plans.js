export default {
  plans: {
    all: 'All',
    pending: 'Pending',
    matching: 'Matching',
    running: 'Running',
    failed: 'Failed',
    completed: 'Completed',
    planDetail: 'Plan Details',
    productDetail: 'Product Details',
    planId: 'Plan ID',
    createTime: 'Create Time',
    minAmount: 'Min Amount',
    maxAmount: 'Max Amount',
    amount: 'Amount',
    progress: 'Progress',
    normalClickPrice: 'Normal Click Price',
    vipClickPrice: 'VIP Click Price',
    startDelivery: 'Start Delivery',
    matching: 'Matching',
    video: 'Video',
    landingPage: 'Landing Page',
    official: 'Official Push',
    unofficial: 'Unofficial Push',
    placementAmount: 'Placement Amount',
    enterAmount: 'Enter Amount',
    confirm: 'Confirm',
    invalidAmount: 'Please enter a valid amount',
    amountTooLow: 'Amount cannot be lower than minimum',
    amountTooHigh: 'Amount cannot exceed maximum limit',
    placementSuccess: 'Placement request submitted',
    placementFailed: 'Placement failed, please try again later',
    viewDetails: 'View Details',
    dataOverview: 'Data Overview',
    refreshInterval: 'Data refreshes every 30 minutes',
    placementProgress: 'Placement Progress',
    consumed: 'Consumed',
    remaining: 'Remaining',
    impressions: 'Impressions',
    clicks: 'Clicks',
    adRevenue: 'Ad Revenue',
    profit: 'Profit',
    placementRules: 'Placement Rules',
    adPlacementStrategy: 'Ad Placement Strategy',
    adPlacementCost: 'Ad Placement Cost',
    paymentMethod: 'Payment Method',
    userTargeting: 'User Targeting',
    location: 'Location',
    adContent: 'Ad Content',
    promotionalLink: 'Promotional Link',
    adDisplayFormat: 'Ad Display Format',
    conversionMethod: 'Conversion Method',
    clickPrice: 'Click Price',
    appDeveloper: 'App Developer',
    backToPlans: 'Back to Plans List',
    planNotFound: 'Plan data not found',
    gameIcon: 'Game Icon',
    gameScreenshot: 'Game Screenshot',
    gameIntroduction: 'Game Introduction',
    category: 'Category',
    developer: 'Developer',
    supportedLanguages: 'Supported Languages',
    ageRating: 'Age Rating',
    version: 'Version',
    size: 'Size',
    installCount: 'Install Count',
    rating: 'Rating',
    lastUpdated: 'Last Updated',
    productNotFound: 'Product not found',
    back: 'Back',
    fetchProductDetailFailed: 'Failed to fetch product details',
    // Coupon related
    coupon: {
      title: 'Coupon',
      notUse: 'No Coupon',
      validity: 'Valid until',
      empty: 'No available coupons',
      useFailed: 'Failed to use coupon'
    },
    // Status mapping
    status: {
      pending: 'Pending',
      matching: 'Matching',
      running: 'Running',
      failed: 'Failed',
      completed: 'Completed'
    }
  }
}