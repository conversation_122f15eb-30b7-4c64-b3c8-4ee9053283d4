/**
 * VIP相关的API请求
 */
import request from '@/utils/request'

/**
 * 获取VIP列表
 * @returns {Promise}
 */
export function getVipList() {
  return request({
    url: '/api/vip/list',
    method: 'get'
  })
}

/**
 * 用户VIP升级
 * @param {Object} data 
 * @param {number} data.vip_id 要升级的VIP等级ID
 * @param {string} data.payment_password 支付密码
 * @returns {Promise}
 */
export function upgradeVip(data) {
  return request({
    url: '/api/vip/upgrade',
    method: 'post',
    data
  })
} 