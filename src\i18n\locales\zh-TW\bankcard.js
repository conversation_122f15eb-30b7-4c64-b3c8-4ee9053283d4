export default {
    bankcard: {
        // 银行卡绑定
        bankCardTitle: '綁定銀行卡',
        bankName: '銀行名稱',
        beneficiaryName: '持卡人姓名',
        accountNumber: '銀行卡賬號',
        routingNumber: '匯款路線號碼',
        beneficiaryAddress: '收款人地址',
        bankAddress: '銀行地址',
        inputBankName: '請輸入銀行名稱',
        inputBeneficiaryName: '請輸入持卡人姓名',
        inputAccountNumber: '請輸入銀行卡賬號',
        inputRoutingNumber: '請輸入匯款路線號碼',
        inputBeneficiaryAddress: '請輸入收款人地址',
        inputBankAddress: '請輸入銀行地址',
        submit: '提交',

        // 表单验证
        fieldRequired: '{field}不能為空',
        submitSuccess: '提交成功',
    }
} 