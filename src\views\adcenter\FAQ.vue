<template>
  <nav-bar :title="t('adcenter.faqDetail.title')" />
  <div class="content">
    <div class="faq-container">
      <div class="faq-item" v-if="currentFaq">
        <div class="faq-question">
          <span>{{ currentFaq.question }}</span>
        </div>
        <div class="faq-answer">
          {{ currentFaq.answer }}
        </div>
      </div>
      <van-empty v-else class="custom-empty" image="search" :description="t('adcenter.faqDetail.notFound')" />
    </div>
  </div>
</template>

<script setup>
import NavBar from '@/components/NavBar.vue'
import { useTitle } from '@/utils/useTitle'

import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { Empty as VanEmpty } from 'vant'
import { useI18n } from 'vue-i18n'

const route = useRoute()
const { t } = useI18n()

// 获取路由参数中的问题ID
const faqId = computed(() => {
  return parseInt(route.query.id || '0')
})

// 设置页面标题
useTitle(computed(() => t('adcenter.faqDetail.title')))

// 常见问题列表 - 使用国际化翻译
const faqList = computed(() => [
  {
    id: 0,
    question: t('adcenter.faqList.whatIsAdUnion'),
    answer: t('adcenter.faqDetail.answers.whatIsAdUnion')
  },
  {
    id: 1,
    question: t('adcenter.faqList.whyCantCancel'),
    answer: t('adcenter.faqDetail.answers.whyCantCancel')
  },
  {
    id: 2,
    question: t('adcenter.faqList.onlineRecharge'),
    answer: t('adcenter.faqDetail.answers.onlineRecharge')
  },
  {
    id: 3,
    question: t('adcenter.faqList.completionTime'),
    answer: t('adcenter.faqDetail.answers.completionTime')
  },
  {
    id: 4,
    question: t('adcenter.faqList.becomeMerchant'),
    answer: t('adcenter.faqDetail.answers.becomeMerchant')
  }
])

// 根据ID获取当前问题
const currentFaq = computed(() => {
  return faqList.value.find(item => item.id === faqId.value) || null
})
</script>

<style scoped>
.faq-container {
  padding: 0.16rem;
  height: 100%;
}

.faq-item {
  background: #fff;
  border-radius: 0.08rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  overflow: hidden;
  min-height: calc(100vh - 1rem);
  display: flex;
  flex-direction: column;
}

.faq-question {
  padding: 0.2rem;
  font-size: 0.18rem;
  font-weight: bold;
  color: #333;
  border-bottom: 1px solid #f5f5f5;
  background-color: #f9f9f9;
}

.faq-answer {
  padding: 0.2rem;
  font-size: 0.15rem;
  color: #333;
  line-height: 1.8;
  flex: 1;
}

.custom-empty {
  min-height: calc(100vh - 1rem);
  display: flex;
  flex-direction: column;
  justify-content: center;
  background: #fff;
  border-radius: 0.08rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}
</style>