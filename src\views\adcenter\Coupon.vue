<template>
  <nav-bar :title="t('coupon.title')" />
  <div class="content">
    <van-tabs v-model:active="activeTab" @change="handleTabChange">
      <van-tab :title="t('coupon.tabs.all')" />
      <van-tab :title="t('coupon.tabs.unused')" />
      <van-tab :title="t('coupon.tabs.used')" />
    </van-tabs>

    <van-empty v-if="couponList.length === 0 && !loading" :description="t('coupon.empty')" image="search" />
    <van-list v-else v-model:loading="loading" :finished="finished" :finished-text="t('noMoreData')"
      @load="onLoad">
      <div v-for="(item, index) in couponList" :key="index" class="coupon-item"
        :class="{ 'used': item.status === 'used' }">
        <div class="coupon-left">
          <div class="coupon-amount">
            <span class="currency">{{ item.type === 0 ? '%' : "$" }}</span>
            <span class="value">{{ formatValue(item) }}</span>
          </div>
          <div class="coupon-threshold">{{ (item.minSpend - 0) !== 0 ? t('coupon.minConsumption', {
            amount:
              formatMinSpend(item.minSpend)
          }) : t('coupon.nolimit')
          }}</div>
        </div>
        <div class="coupon-right">
          <div class="coupon-name">{{ item.name }}</div>
          <div class="coupon-time">{{ t('coupon.validity') }} {{ formatExpireDate(item.endTime) }}</div>
          <div class="coupon-desc">{{ item.description }}</div>
          <div class="coupon-status" v-if="item.status === 'used'">{{ t('coupon.used') }}</div>
        </div>
      </div>

    </van-list>
  </div>
</template>

<script setup>
import NavBar from '@/components/NavBar.vue'
import { useTitle } from '@/utils/useTitle'
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { couponApi } from '@/api'
import {
  Tabs as VanTabs,
  Tab as VanTab,
  List as VanList,
  Empty as VanEmpty,
  showToast
} from 'vant'

const { t } = useI18n()

// 设置页面标题
useTitle(t('coupon.title'))

// 选项卡状态
const activeTab = ref(0)

// 列表加载状态
const loading = ref(false)
const finished = ref(false)
const page = ref(1)
const pageSize = ref(10)

// 优惠券列表数据
const couponList = ref([])

// 处理Tab切换
const handleTabChange = (index) => {
  // 重置列表状态
  couponList.value = []
  page.value = 1
  finished.value = false
  // 加载新数据
  loadCoupons()
}

// 获取优惠券状态
const getCouponStatus = () => {
  const statusMap = ['all', 'unused', 'used']
  return statusMap[activeTab.value]
}

// 格式化金额显示
const formatValue = (item) => {
  console.log(item.value)
  if (item.type === 0) {
    return parseFloat(item.value - 0) // 百分比显示，例如0.8显示为8折
  }
  return item.value
}

// 格式化最低消费
const formatMinSpend = (minSpend) => {
  return parseFloat(minSpend).toLocaleString()
}

// 格式化到期日期
const formatExpireDate = (endTime) => {
  if (!endTime) return ''
  return new Date(endTime).toLocaleDateString()
}

// 加载优惠券数据
const loadCoupons = async () => {
  if (loading.value) return

  loading.value = true
  try {
    const result = await couponApi.fetchCouponList({
      page: page.value,
      pageSize: pageSize.value,
      status: getCouponStatus()
    })

    // 处理API返回的数据格式
    if (result && result.code === 200 && result.data) {
      const { total, list } = result.data

      if (list && list.length > 0) {
        couponList.value = [...couponList.value, ...list]

        // 判断是否加载完所有数据
        if (couponList.value.length >= total) {
          finished.value = true
        } else {
          page.value++
        }
      } else {
        finished.value = true
      }
    } else {
      finished.value = true
    }
  } catch (error) {
    console.error('获取优惠券列表失败:', error)
    showToast(t('error.loadFailed'))
    finished.value = true
  } finally {
    loading.value = false
  }
}

// 加载数据
const onLoad = () => {
  loadCoupons()
}

// 组件挂载时加载数据
onMounted(() => {
  loadCoupons()
})
</script>

<style scoped>
.content {
  padding: 0;
}

.coupon-item {
  display: flex;
  margin: 0.16rem;
  border-radius: 0.08rem;
  background: #fff;
  box-shadow: 0 0.02rem 0.08rem rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
}

.coupon-item.used {
  opacity: 0.6;
}

.coupon-left {
  width: 1.2rem;
  background-color: #1877F2;
  color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.16rem;
  position: relative;
}

.coupon-left::after {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 0.08rem;
  background-image: radial-gradient(circle at 0 0.08rem, transparent 0.08rem, #fff 0);
  background-size: 0.08rem 0.16rem;
  background-repeat: repeat-y;
}

.coupon-amount {
  font-weight: bold;
  display: flex;
  align-items: baseline;
}

.currency {
  font-size: 0.16rem;
}

.value {
  font-size: 0.32rem;
}

.coupon-threshold {
  font-size: 0.12rem;
  margin-top: 0.08rem;
}

.coupon-right {
  flex: 1;
  padding: 0.16rem;
  position: relative;
}

.coupon-name {
  font-size: 0.16rem;
  font-weight: bold;
  margin-bottom: 0.08rem;
}

.coupon-time {
  font-size: 0.12rem;
  color: #999;
  margin-bottom: 0.08rem;
}

.coupon-desc {
  font-size: 0.12rem;
  color: #666;
}

.coupon-status {
  position: absolute;
  right: 0.16rem;
  top: 0.16rem;
  color: #999;
  font-size: 0.14rem;
  border: 1px solid #999;
  padding: 0.02rem 0.08rem;
  border-radius: 0.04rem;
  transform: rotate(15deg);
}
</style>