import { watch, onMounted } from 'vue'
import { useRoute } from 'vue-router'

// 获取应用名称
const APP_TITLE = import.meta.env.VITE_APP_TITLE || 'Facebook ADS'

/**
 * 设置页面标题的钩子函数
 * @param {String | Function} title 页面标题或返回标题的函数
 * @param {Boolean} withAppName 是否包含应用名称
 */
export function useTitle(title, withAppName = true) {
  const route = useRoute()
  
  const setTitle = (t) => {
    const pageTitle = typeof t === 'function' ? t() : t
    document.title = withAppName ? `${pageTitle} - ${APP_TITLE}` : pageTitle
  }
  
  onMounted(() => {
    setTitle(title)
  })
  
  // 当路由参数变化时更新标题
  if (typeof title === 'function') {
    watch(() => route.params, () => {
      setTitle(title)
    }, { deep: true })
  }
} 