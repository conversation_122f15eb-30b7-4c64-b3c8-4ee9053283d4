<template>
  <nav-bar :title="t('agent.title')" />
  <div class="content">
    <div class="banner">
      <img src="@/assets/image/adcenter/agent/banner.png" alt="banner" />
    </div>

    <div class="agent-id">
      <p>{{ t('agent.currentAgentId') }}</p>
      <div class="id-boxes">
        <div class="id-box" v-for="(digit, index) in agentIdArray" :key="index">{{ digit }}</div>
      </div>
    </div>

    <div class="follow-btn">
      <van-button type="primary" block @click="showActionSheet = true">{{ t('agent.followButton') }}</van-button>
    </div>

    <van-tabs v-model:active="activeTab" @change="handleTabChange">
      <van-tab :title="t('agent.tabs.rules')">
        <div class="rules-content">
          <ol>
            <li><span class="dot"></span>{{ t('agent.rules.rule1') }}</li>
            <li><span class="dot"></span>{{ t('agent.rules.rule2') }}</li>
            <li><span class="dot"></span>{{ t('agent.rules.rule3') }}</li>
            <li><span class="dot"></span>{{ t('agent.rules.rule4') }}</li>
            <li><span class="dot"></span>{{ t('agent.rules.rule5') }}</li>
          </ol>
        </div>
      </van-tab>
      <van-tab :title="t('agent.tabs.records')">
        <van-list v-model:loading="loading" :finished="finished" :finished-text="t('agent.recordList.noMore')"
          @load="loadMoreRecords">
          <div class="record-item" v-for="(item, index) in followRecords" :key="index">
            <div class="record-info">
              <div class="avatar-placeholder">
                <img :src="item.avatar" alt="avatar" />
              </div>
              <div class="record-details">
                <div class="record-name">{{ item.nickname }}</div>
                <div class="record-time">{{ item.createTime }}</div>
              </div>
            </div>
            <div class="record-status" :class="{
              'status-active': parseInt(item.status) === 1,
              'status-ended': parseInt(item.status) === 2,
              'status-pending': parseInt(item.status) === 0
            }">
              {{ getStatusText(item.status) }}
            </div>
          </div>
          <van-empty v-if="followRecords.length === 0" :description="t('agent.recordList.empty')" />
        </van-list>
      </van-tab>
    </van-tabs>

    <van-action-sheet v-model:show="showActionSheet" :title="t('agent.actionSheet.title')"
      :close-on-click-overlay="true">
      <div class="action-sheet-content">
        <div class="input-wrapper">
          <p>{{ t('agent.actionSheet.inputLabel') }}</p>
          <van-field v-model="inputAgentId" :placeholder="t('agent.actionSheet.placeholder')" />
        </div>
        <div class="confirm-btn">
          <van-button round block type="primary" :disabled="!inputAgentId" @click="confirmFollow"
            :loading="submitLoading">{{
              t('agent.actionSheet.confirm') }}</van-button>
        </div>
      </div>
    </van-action-sheet>

    <van-toast id="van-toast" />
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import NavBar from '@/components/NavBar.vue'
import { followAgency, getFollowRecords } from '@/api/agency'
import { useUserStore } from '@/stores/user'
import { getUserInfo } from '@/utils/useUserInfo'
import {
  Button as VanButton,
  Tabs as VanTabs,
  Tab as VanTab,
  List as VanList,
  ActionSheet as VanActionSheet,
  Field as VanField,
  Empty as VanEmpty,
  showToast
} from 'vant'

const { t } = useI18n()
const userStore = useUserStore()

// 代运营ID
const agentId = computed(() => userStore.userInfo.agencyId || '000000')
const agentIdArray = computed(() => agentId.value.split(''))

// 标签页控制
const activeTab = ref(0)

// 跟随记录
const followRecords = ref([])
const loading = ref(false)
const finished = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const submitLoading = ref(false)

// 处理标签页切换
const handleTabChange = (index) => {
  if (index === 1) { // 跟随记录标签
    // 重置数据并重新加载
    followRecords.value = []
    currentPage.value = 1
    finished.value = false
    loadMoreRecords()
  }
}

// 获取跟随记录
const loadMoreRecords = async () => {
  if (finished.value) return

  loading.value = true
  try {
    const res = await getFollowRecords({
      page: currentPage.value,
      pageSize: pageSize.value
    })

    console.log('获取跟随记录结果:', res)

    // 确保正确处理返回的数据结构
    if (res && res.data) {
      const { list, total } = res.data;

      if (list && list.length > 0) {
        followRecords.value = [...followRecords.value, ...list]
        currentPage.value++
      }

      if (total && followRecords.value.length >= total || !list || list.length === 0) {
        finished.value = true
      }
    } else {
      finished.value = true
    }
  } catch (error) {
    console.error('获取跟随记录失败', error)
    showToast(error.message || t('agent.errors.loadFailed'))
  } finally {
    loading.value = false
  }
}

// 获取状态文本
const getStatusText = (status) => {
  // 根据新的字典定义处理状态
  switch (parseInt(status)) {
    case 1:
      return t('agent.recordList.active')
    case 2:
      return t('agent.recordList.ended')
    case 0:
      return t('agent.recordList.pending')
    default:
      return status
  }
}

// ActionSheet控制
const showActionSheet = ref(false)
const inputAgentId = ref('')

// 确认跟随
const confirmFollow = async () => {
  if (!inputAgentId.value) return

  submitLoading.value = true
  try {
    const res = await followAgency(inputAgentId.value)
    console.log('提交跟随申请结果:', res)
    showToast(t('agent.success.followSubmitted'))
    showActionSheet.value = false
    inputAgentId.value = ''

    // 重新加载跟随记录
    followRecords.value = []
    currentPage.value = 1
    finished.value = false

    // 如果当前是在跟随记录标签，则立即加载
    if (activeTab.value === 1) {
      loadMoreRecords()
    }
  } catch (error) {
    console.error('提交跟随申请失败', error)
    showToast(error.message || t('agent.errors.followFailed'))
  } finally {
    submitLoading.value = false
  }
}

// 页面加载时获取用户信息和跟随记录
onMounted(async () => {
  try {
    // 获取用户信息
    await getUserInfo()

    // 如果当前是跟随记录标签，则加载记录
    if (activeTab.value === 1) {
      loadMoreRecords()
    }
  } catch (error) {
    console.error('初始化页面失败', error)
  }
})

// 监听用户信息变化
watch(() => userStore.userInfo, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0 && activeTab.value === 1) {
    // 用户信息加载完成后，如果在跟随记录标签，则重新加载记录
    followRecords.value = []
    currentPage.value = 1
    finished.value = false
    loadMoreRecords()
  }
}, { deep: true })
</script>

<style scoped>
.content {
  padding: 0;
}

.banner {
  width: 100%;
}

.banner img {
  width: 100%;
  display: block;
}

.agent-id {
  text-align: center;
  padding: 0.3rem 0;
}

.agent-id p {
  font-size: 0.14rem;
  color: #666;
  margin-bottom: 0.2rem;
}

.id-boxes {
  display: flex;
  justify-content: center;
  gap: 0.1rem;
}

.id-box {
  width: 0.4rem;
  height: 0.5rem;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.24rem;
  font-weight: bold;
}

.follow-btn {
  padding: 0.18rem;
  margin-bottom: 0.16rem;
}

.follow-btn :deep(.van-button--primary) {
  background-color: #1877F2;
  border-color: #1877F2;
  font-size: 0.14rem;
  padding: 0.2rem 0;
}

.rules-content {
  padding: 0.16rem;
}


.rules-content li {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0.12rem;
  font-size: 0.14rem;
  line-height: 1.6;
  color: #666;
}

.rules-content li .dot {
  display: inline-block;
  width: 0.08rem;
  height: 0.08rem;
  background-color: #1877F2;
  border-radius: 50%;
  margin-right: 0.1rem;
  margin-top: 0.06rem;
  flex-shrink: 0;
}

.record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.16rem;
  border-bottom: 1px solid #f5f5f5;
}

.record-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.avatar-placeholder {
  width: 0.5rem;
  height: 0.5rem;
  background-color: #f5f5f5;
  margin-right: 0.16rem;
}

.record-details {
  flex: 1;
}

.record-name {
  font-size: 0.16rem;
  margin-bottom: 0.05rem;
}

.record-time {
  font-size: 0.12rem;
  color: #999;
  margin-bottom: 0.05rem;
}

.record-performance {
  font-size: 0.12rem;
  color: #666;
  display: flex;
  gap: 0.1rem;
}

.record-status {
  font-size: 0.14rem;
  padding-left: 0.16rem;
}

.status-active {
  color: #1877F2;
}

.status-ended {
  color: #999;
}

.status-pending {
  color: #ff9800;
}

.action-sheet-content {
  padding: 0.16rem 0.16rem 0.3rem;
}

.input-wrapper {
  margin-bottom: 0.3rem;
  border-bottom: 1px solid #eee;
}

.input-wrapper p {
  margin-bottom: 0.16rem;
  font-size: 0.14rem;
}

.confirm-btn {
  margin-top: 0.3rem;
}

.confirm-btn :deep(.van-button--primary) {
  background-color: #1877F2;
  border-color: #1877F2;
}

.van-tabs :deep(.van-tab--active) {
  color: #1877F2;
}

.van-tabs :deep(.van-tabs__line) {
  background-color: #1877F2;
}
</style>