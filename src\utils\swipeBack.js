import { useRouter } from 'vue-router'

/**
 * 滑动返回功能
 * 从左至右滑动时触发返回操作
 */
export function useSwipeBack() {
  const router = useRouter()

  // 初始化触摸参数
  let startX = 0
  let startY = 0
  let startTime = 0
  const threshold = 100 // 滑动阈值，超过这个距离才触发返回
  const restraint = 100 // 垂直方向约束，小于这个值才认为是水平滑动
  const allowedTime = 300 // 允许的最大滑动时间，单位毫秒

  // 添加触摸事件监听
  const initSwipeBack = () => {
    document.addEventListener('touchstart', handleTouchStart, { passive: true })
    document.addEventListener('touchend', handleTouchEnd, { passive: true })
  }

  // 移除触摸事件监听
  const destroySwipeBack = () => {
    document.removeEventListener('touchstart', handleTouchStart)
    document.removeEventListener('touchend', handleTouchEnd)
  }

  // 处理触摸开始事件
  const handleTouchStart = (e) => {
    // 检查当前路由是否禁用了滑动返回
    // if (isSwipeBackDisabled()) return

    const touchObj = e.changedTouches[0]
    startX = touchObj.pageX
    startY = touchObj.pageY
    startTime = new Date().getTime()
  }

  // 处理触摸结束事件
  const handleTouchEnd = (e) => {
    // 检查当前路由是否禁用了滑动返回

    const touchObj = e.changedTouches[0]
    const distX = touchObj.pageX - startX // 水平滑动距离
    const distY = Math.abs(touchObj.pageY - startY) // 垂直滑动距离
    const elapsedTime = new Date().getTime() - startTime // 滑动时间

    // 判断是否满足滑动返回条件：
    // 1. 从左向右滑动
    // 2. 水平滑动距离超过阈值
    // 3. 垂直滑动距离小于约束值
    // 4. 滑动时间小于允许的最大时间
    // 5. 起始点在屏幕左侧边缘区域（宽度的20%以内）
    const edgeWidth = window.innerWidth * 0.2

    if (
      distX > threshold &&
      distY < restraint &&
      elapsedTime < allowedTime &&
      startX < edgeWidth
    ) {
      if (isSwipeBackDisabled()) {
        console.log('isSwipeBackDisabled')
        goBack()
      } else {
        // 触发返回操作
        router.back()
      }
    }
  }
  const goBack = () => {
    const userAgent = navigator.userAgent.toLowerCase();
    console.log('userAgent', userAgent)
    if (userAgent.indexOf('android') !== -1) {
      // 安卓系统执行的代码
      if (window.AppTweak) {
        window.AppTweak.ClosePage();
      }
    } else if (userAgent.indexOf('iphone') !== -1 || userAgent.indexOf('ipad') !== -1) {
      // iOS系统执行的代码
      if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.AppTweak) {
        window.webkit.messageHandlers.AppTweak.postMessage("ClosePage");
      }
    }
  }



  // 检查当前路由是否禁用了滑动返回
  const isSwipeBackDisabled = () => {
    const currentRoute = router.currentRoute.value
    return currentRoute.meta.disableSwipeBack === true
  }

  return {
    initSwipeBack,
    destroySwipeBack
  }
} 