<template>
  <nav-bar :title="t('notification.title')" :show-bell="false" />
  <div class="content">
    <van-dropdown-menu>
      <van-dropdown-item v-model="notificationType" :options="notificationTypeOptions" @change="handleFilterChange" />
      <van-dropdown-item v-model="notificationStatus" :options="notificationStatusOptions"
        @change="handleFilterChange" />
    </van-dropdown-menu>
      <van-empty v-if="notificationList.length === 0 && !loading" :description="t('notification.emptyList')" />
    <van-list v-else v-model:loading="loading" :finished="finished" :finished-text="t('noMoreData')" @load="onLoad"
      :error="loadError" :error-text="t('loadError')">

      
      <div v-for="(item, index) in notificationList" :key="item.id" class="notification-item"
        :class="[getItemShadowClass(item.type), { 'no-shadow': item.isRead }]" @click="showNotificationDetail(item)">
        <div class="notification-category" :class="{ 'unread-category': !item.isRead }">
          <van-tag :type="getTagType(item.type)" size="small">{{ getCategoryText(item.type) }}</van-tag>
          <div class="notification-time">
            <div class="notification-time-text">{{ formatTime(item.createTime) }}</div>
            <div class="unread-dot" v-if="!item.isRead"></div>
          </div>
        </div>
        <div class="notification-content">
          <div class="notification-icon" :class="getIconClass(item.type)">
            <van-icon :name="getIconByCategory(item.type)" />
          </div>
          <div class="notification-info">
            <div class="notification-title">{{ item.title }}</div>
            <div class="notification-desc">{{ item.content }}</div>
            <div class="notification-status" v-if="item.status !== 'completed'">
              <van-tag round :type="getStatusTagType(item.status)" size="small">{{ getStatusText(item.status)
              }}</van-tag>
            </div>
          </div>
        </div>
      </div>
    </van-list>
  </div>

  <van-dialog v-model:show="showDialog" :title="currentNotification.title" :showConfirmButton="true"
    :confirmButtonText="t('notification.confirm')">
    <div class="dialog-content">
      <div class="dialog-text">{{ currentNotification.content }}</div>
      <div class="dialog-link" v-if="currentNotification.link">
        <van-button type="primary" size="small" @click="handleNotificationLink">{{ t('notification.viewDetails')
        }}</van-button>
      </div>
    </div>
  </van-dialog>

  <van-action-bar v-if="unreadCount > 0">
    <van-action-bar-button type="primary" :text="t('notification.markAllRead')" @click="handleMarkAllRead" />
  </van-action-bar>
</template>

<script setup>
import NavBar from '@/components/NavBar.vue'
import { useTitle } from '@/utils/useTitle'
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { getNotifications, readNotification, readAllNotifications } from '@/api/notification'
import {
  DropdownMenu as VanDropdownMenu,
  DropdownItem as VanDropdownItem,
  List as VanList,
  Dialog as VanDialog,
  Icon as VanIcon,
  Tag as VanTag,
  ActionBar as VanActionBar,
  ActionBarButton as VanActionBarButton,
  Button as VanButton,
  showToast
} from 'vant'

const { t } = useI18n()
const router = useRouter()

// 设置页面标题
useTitle(t('notification.title'))

// 下拉菜单选项
const notificationType = ref('all')
const notificationTypeOptions = computed(() => [
  { text: t('notification.filters.all'), value: 'all' },
  { text: t('notification.filters.system'), value: 'system' },
  { text: t('notification.filters.activity'), value: 'activity' },
  { text: t('notification.filters.maintenance'), value: 'maintenance' }
])

const notificationStatus = ref('all')
const notificationStatusOptions = computed(() => [
  { text: t('notification.filters.allStatus'), value: 'all' },
  { text: t('notification.filters.unread'), value: 'unread' },
  { text: t('notification.filters.read'), value: 'read' }
])

// 消息列表数据
const notificationList = ref([])
const unreadCount = ref(0)

// 列表加载状态
const loading = ref(false)
const finished = ref(false)
const loadError = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const totalItems = ref(0)

// 弹窗相关
const showDialog = ref(false)
const currentNotification = ref({})

// 获取通知列表
const fetchNotifications = async (isRefresh = false) => {
  if (isRefresh) {
    currentPage.value = 1
    notificationList.value = []
  }

  try {
    loading.value = true
    loadError.value = false

    const params = {
      messageType: notificationType.value,
      page: currentPage.value,
      pageSize: pageSize.value
    }

    // 根据筛选条件处理
    if (notificationStatus.value === 'unread') {
      params.isRead = 0
    } else if (notificationStatus.value === 'read') {
      params.isRead = 1
    }

    const res = await getNotifications(params)

    if (res && res.code === 200 && res.data) {
      if (isRefresh) {
        notificationList.value = res.data.list || []
      } else {
        notificationList.value = [...notificationList.value, ...(res.data.list || [])]
      }

      totalItems.value = res.data.total || 0
      unreadCount.value = res.data.unreadCount || 0

      // 判断是否加载完毕
      finished.value = notificationList.value.length >= totalItems.value
      currentPage.value++
    } else {
      finished.value = true
    }
  } catch (error) {
    console.error('获取通知列表失败', error)
    loadError.value = true
  } finally {
    loading.value = false
  }
}

// 处理筛选条件变化
const handleFilterChange = () => {
  fetchNotifications(true)
}

// 加载更多数据
const onLoad = () => {
  fetchNotifications()
}

// 初始化加载数据
onMounted(() => {
  fetchNotifications()
})

// 根据分类获取国际化文本
const getCategoryText = (type) => {
  switch (type) {
    case 'activity':
      return t('notification.categories.activity')
    case 'maintenance':
      return t('notification.categories.maintenance')
    case 'system':
      return t('notification.categories.system')
    default:
      return type
  }
}

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'pending':
      return t('notification.status.pending')
    case 'processing':
      return t('notification.status.processing')
    case 'completed':
      return t('notification.status.completed')
    default:
      return status
  }
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  switch (status) {
    case 'pending':
      return 'warning'
    case 'processing':
      return 'primary'
    case 'completed':
      return 'success'
    default:
      return 'default'
  }
}

// 格式化时间
const formatTime = (time) => {
  if (!time) return ''

  try {
    const date = new Date(time)
    const now = new Date()
    const diff = now - date

    // 小于1小时，显示分钟
    if (diff < 60 * 60 * 1000) {
      const minutes = Math.floor(diff / (60 * 1000))
      return t('notification.timeAgo.minutesAgo', { n: minutes || 1 })
    }

    // 小于24小时，显示小时
    if (diff < 24 * 60 * 60 * 1000) {
      const hours = Math.floor(diff / (60 * 60 * 1000))
      return t('notification.timeAgo.hoursAgo', { n: hours })
    }

    // 小于30天，显示天数
    if (diff < 30 * 24 * 60 * 60 * 1000) {
      const days = Math.floor(diff / (24 * 60 * 60 * 1000))
      return t('notification.timeAgo.daysAgo', { n: days })
    }

    // 大于30天，显示具体日期
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  } catch (e) {
    return time
  }
}

// 根据分类获取Tag类型
const getTagType = (type) => {
  switch (type) {
    case 'activity':
      return 'danger'
    case 'maintenance':
      return 'warning'
    case 'system':
      return 'primary'
    default:
      return 'default'
  }
}

// 根据分类获取Icon样式类
const getIconClass = (type) => {
  switch (type) {
    case 'activity':
      return 'icon-activity'
    case 'maintenance':
      return 'icon-maintenance'
    case 'system':
      return 'icon-system'
    default:
      return ''
  }
}

// 根据分类获取图标
const getIconByCategory = (type) => {
  switch (type) {
    case 'activity':
      return 'gift-o'
    case 'maintenance':
      return 'setting-o'
    case 'system':
      return 'volume-o'
    default:
      return 'info-o'
  }
}

// 根据分类获取Item阴影样式类
const getItemShadowClass = (type) => {
  switch (type) {
    case 'activity':
      return 'item-shadow-activity'
    case 'maintenance':
      return 'item-shadow-maintenance'
    case 'system':
      return 'item-shadow-system'
    default:
      return ''
  }
}

// 显示消息详情
const showNotificationDetail = async (notification) => {
  currentNotification.value = notification
  showDialog.value = true

  // 如果未读，调用接口标记为已读
  if (!notification.isRead) {
    try {
      await readNotification(notification.id)

      // 更新本地状态
      const index = notificationList.value.findIndex(item => item.id === notification.id)
      if (index !== -1) {
        notificationList.value[index].isRead = true
      }

      // 更新未读数量
      if (unreadCount.value > 0) {
        unreadCount.value--
      }
    } catch (error) {
      console.error('标记通知为已读失败', error)
    }
  }
}

// 处理通知链接跳转
const handleNotificationLink = () => {
  if (currentNotification.value.link) {
    router.push(currentNotification.value.link)
    showDialog.value = false
  }
}

// 标记所有为已读
const handleMarkAllRead = async () => {
  try {
    await readAllNotifications()

    // 更新本地状态
    notificationList.value.forEach(item => {
      item.isRead = true
    })

    // 更新未读数量
    unreadCount.value = 0

    showToast(t('notification.markAllReadSuccess'))
  } catch (error) {
    console.error('标记所有通知为已读失败', error)
    showToast(t('notification.markAllReadFailed'))
  }
}
</script>

<style scoped>
.content {
  padding: 0;
  background-color: #f7f8fa;
  min-height: calc(100vh - 0.46rem);
}

.notification-item {
  margin: 0.1rem;
  background-color: #fff;
  border-radius: 0.08rem;
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
}

.item-shadow-activity {
  box-shadow: 0 2px 8px rgba(238, 10, 36, 0.15);
}

.item-shadow-maintenance {
  box-shadow: 0 2px 8px rgba(255, 151, 106, 0.15);
}

.item-shadow-system {
  box-shadow: 0 2px 8px rgba(25, 137, 250, 0.15);
}

.no-shadow {
  box-shadow: none;
}

.notification-category {
  padding: 0.06rem 0.1rem;
  font-size: 0.12rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f5f5f5;
}

.unread-category {
  background-color: #ecf5ff;
  border-bottom-color: #d4e6fd;
}

.notification-content {
  padding: 0.08rem;
  display: flex;
  align-items: center;
}

.notification-icon {
  width: 0.3rem;
  height: 0.3rem;
  border-radius: .05rem;
  background-color: #f8f8f8;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 0.1rem;
  color: #666;
}

.icon-activity {
  background-color: #ffeded;
  color: #ee0a24;
}

.icon-maintenance {
  background-color: #fff7e8;
  color: #ff976a;
}

.icon-system {
  background-color: #e8f3ff;
  color: #1989fa;
}

.notification-info {
  flex: 1;
}

.notification-title {
  font-size: 0.14rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.05rem;
}

.notification-desc {
  font-size: 0.12rem;
  color: #999;
}

.notification-status {
  margin-top: 0.05rem;
}

.notification-time {
  font-size: 0.1rem;
  color: #999;
  display: flex;
  align-items: center;
}

.unread-dot {
  width: 0.08rem;
  height: 0.08rem;
  border-radius: 50%;
  background-color: #ff4d4f;
  margin-left: .1rem;
}

.dialog-content {
  padding: 0.16rem;
}

.dialog-text {
  font-size: 0.14rem;
  line-height: 1.6;
  color: #333;
  text-align: center;
  margin-bottom: 0.16rem;
}

.dialog-link {
  text-align: center;
  margin-top: 0.16rem;
}

.empty-list {
  padding: 0.5rem 0;
  text-align: center;
  color: #999;
  font-size: 0.14rem;
}
</style>