export default {
    bankcard: {
        bankCardTitle: 'Bind Bank Card',
        bankName: 'Bank Name',
        beneficiaryName: 'Beneficiary Name',
        accountNumber: 'Account Number',
        routingNumber: 'ABA Routing Number',
        beneficiaryAddress: 'Beneficiary Address',
        bankAddress: 'Bank Address',
        inputBankName: 'Please enter bank name',
        inputBeneficiaryName: 'Please enter beneficiary name',
        inputAccountNumber: 'Please enter account number',
        inputRoutingNumber: 'Please enter routing number',
        inputBeneficiaryAddress: 'Please enter beneficiary address',
        inputBankAddress: 'Please enter bank address',
        submit: 'Submit',

        // Form Validation
        fieldRequired: '{field} cannot be empty',
        submitSuccess: 'Submitted successfully',
    }
}
