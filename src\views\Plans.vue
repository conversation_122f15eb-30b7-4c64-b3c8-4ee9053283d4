<template>
  <nav-bar :title="t('plansCenter')" @click-left="goBack" :useDefaultNavigation="false" />
  <div class="content">
    <van-tabs v-model:active="activeTab" sticky offset-top="46" @change="handleTabChange">
      <van-tab :title="t('plans.all')">
        <plan-list :plans="plans" :is-loading="loading" :has-error="error" @retry="fetchPlans" />
      </van-tab>
      <van-tab :title="t('plans.pending')">
        <plan-list :plans="plans" :is-loading="loading" :has-error="error" @retry="fetchPlans" />
      </van-tab>
      <van-tab :title="t('plans.matching')">
        <plan-list :plans="plans" :is-loading="loading" :has-error="error" @retry="fetchPlans" />
      </van-tab>
      <van-tab :title="t('plans.running')">
        <plan-list :plans="plans" :is-loading="loading" :has-error="error" @retry="fetchPlans" />
      </van-tab>
      <van-tab :title="t('plans.failed')">
        <plan-list :plans="plans" :is-loading="loading" :has-error="error" @retry="fetchPlans" />
      </van-tab>
      <van-tab :title="t('plans.completed')">
        <plan-list :plans="plans" :is-loading="loading" :has-error="error" @retry="fetchPlans" />
      </van-tab>
    </van-tabs>
  </div>
</template>

<script setup>
import { Tab as VanTab, Tabs as VanTabs } from 'vant'
import NavBar from '@/components/NavBar.vue'
import PlanList from '@/components/PlanList.vue'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'
import { ref, onMounted } from 'vue'
import { useTitle } from '@/utils/useTitle'
import { planApi } from '@/api'
import { getUserInfo, loginAndGetUserInfo } from '@/utils/useUserInfo'
import { useUserStore } from '@/stores/user'
import { useRouter } from 'vue-router'

const { t, locale } = useI18n()
const route = useRoute()
const activeTab = ref(0)
const userStore = useUserStore()
const router = useRouter()
// 设置页面标题
useTitle(() => t('plansCenter'))

// 计划数据
const plans = ref([])
// 加载状态
const loading = ref(false)
// 错误状态
const error = ref(false)

// 获取计划列表
const fetchPlans = async () => {
  loading.value = true
  error.value = false
  try {
    const statusMap = {
      0: '全部', // 全部
      1: '待投放',    // 待投放
      2: '匹配中',   // 匹配中
      3: '投放中',    // 投放中
      4: '投放失败',     // 投放失败
      5: '投放完成'   // 投放完成
    }

    const res = await planApi.getPlanList({
      status: statusMap[activeTab.value],
      page: 1,
      pageSize: 10
    })

    if (res && res.data && res.data.list) {
      plans.value = res.data.list
    } else {
      plans.value = []
    }
  } catch (error) {
    console.error('获取计划列表失败:', error)
    plans.value = []
    error.value = true
  } finally {
    loading.value = false
  }
}

const goBack = () => {
  const userAgent = navigator.userAgent.toLowerCase();
  if (userAgent.indexOf('android') !== -1) {
    // 安卓系统执行的代码
    if (window.AppTweak) {
      window.AppTweak.ClosePage();
    }
  } else if (userAgent.indexOf('iphone') !== -1 || userAgent.indexOf('ipad') !== -1) {
    // iOS系统执行的代码
    if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.AppTweak) {
      window.webkit.messageHandlers.AppTweak.postMessage("ClosePage");
    }
  }
}

// 监听标签页变化
const handleTabChange = () => {
  fetchPlans()
}

// 页面挂载时获取数据
onMounted(async () => {
  try {
    // 获取URL中的data参数
    const dataParam = route.query.data

    if (!dataParam) {
      // 获取用户信息，优先从store中获取
      const userInfo = await getUserInfo()
      // 如果获取失败，可以添加错误处理
      if (!userInfo) {
        console.error('获取用户信息失败')
      }
    } else {
      // 如果data存在，使用data参数登录并获取用户信息
      // 这里传入data参数，会强制更新用户信息
      const userInfo = await loginAndGetUserInfo(dataParam)
      if (userInfo) {
        locale.value = userInfo.fb_lang
        router.push('/plans')
      } else {
        console.error('登录失败或获取用户信息失败')
      }
    }
    if (!userStore.userInfo.auth_code) {
      router.push('/account?url=plans')
    }

  } catch (error) {
    console.error('请求数据失败:', error)
  } finally {
    // 无论请求成功或失败，都关闭加载状态
    loading.value = false
  }
  
  // 请求计划列表数据
  fetchPlans()
})
</script>

<style scoped>
.van-tabs {
  background: #fff;
}
</style>