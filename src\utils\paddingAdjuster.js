/**
 * 根据localStorage中的facebook_ad_user_info的tab_top值设置顶部内边距
 */

const STORAGE_KEY = 'facebook_ad_user_info'

/**
 * 应用顶部内边距
 * 读取localStorage中的facebook_ad_user_info中的tab_top值
 * 并设置为#app和.van-nav-bar的padding-top
 */
export function applyTopPadding() {
  try {
    // 从localStorage获取用户信息
    const userInfoStr = localStorage.getItem(STORAGE_KEY)
    if (!userInfoStr) return
    
    const userInfo = JSON.parse(userInfoStr)
    const tabTop = userInfo.tab_top
    
    // 如果tab_top存在且为数字
    if (tabTop !== undefined && !isNaN(Number(tabTop))) {
      const paddingValue = `${tabTop}px`
      
      // 设置#app的padding-top
      const appElement = document.getElementById('app')
      if (appElement) {
        appElement.style.paddingTop = paddingValue
      }
      
      // 创建或更新CSS变量
      const root = document.documentElement
      root.style.setProperty('--van-nav-bar-padding-top', paddingValue)
      
      // 创建或更新全局样式
      let styleElement = document.getElementById('van-nav-bar-padding-style')
      if (!styleElement) {
        styleElement = document.createElement('style')
        styleElement.id = 'van-nav-bar-padding-style'
        document.head.appendChild(styleElement)
      }
      
      // 设置样式规则，确保能覆盖vant的默认样式
      styleElement.textContent = `
        .van-nav-bar {
          padding-top: ${tabTop}px !important;
        }
      `
      
    }
  } catch (error) {
    console.error('Error applying top padding:', error)
  }
}

/**
 * 更新顶部内边距
 * 当用户信息发生变化时调用此函数
 */
export function updateTopPadding() {
  applyTopPadding()
}

export default {
  applyTopPadding,
  updateTopPadding
}