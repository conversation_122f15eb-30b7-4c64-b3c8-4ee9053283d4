/**
 * 钱包相关的API请求
 */
import request from '@/utils/request'

/**
 * 获取钱包信息
 * @returns {Promise}
 */
export function fetchWalletInfo() {
  return request({
    url: '/api/wallet/info',
    method: 'get'
  })
}

/**
 * 获取钱包交易记录
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function fetchWalletTransactions(params) {
  return request({
    url: '/api/wallet/transactions',
    method: 'get',
    params
  })
}

/**
 * 设置交易密码
 * @param {Object} data 密码数据
 * @param {string} data.password 交易密码
 * @param {string} data.confirmPassword 确认交易密码
 * @returns {Promise}
 */
export function setTransactionPassword(data) {
  return request({
    url: 'api/wallet/payment-password',
    method: 'post',
    data
  })
}

/**
 * 提现申请
 * @param {Object} data 提现数据
 * @param {number} data.amount 提现金额
 * @param {string} data.password 交易密码
 * @returns {Promise}
 */
export function withdraw(data) {
  return request({
    url: '/api/wallet/withdraw',
    method: 'post',
    data
  })
}

/**
 * 验证支付密码
 * @param {Object} data
 * @param {String} data.payPassword 支付密码
 * @returns {Promise}
 */
export function verifyPaymentPassword(data) {
  return request({
    url: 'api/wallet/payment-password/verify',
    method: 'post',
    data
  })
}

/**
 * 获取用户绑定的银行卡或加密货币信息
 * @returns {Promise}
 */
export function fetchBindingInfo(data) {
  return request({
    url: '/api/wallet/binding-info?type=' + data.type,
    method: 'get',
  })
}

/**
 * 提交银行卡信息
 * @param {Object} data 银行卡数据
 * @param {string} data.cardNumber 卡号
 * @param {string} data.holderName 持卡人姓名
 * @param {string} data.bankName 银行名称
 * @param {string} data.branch 支行名称
 * @returns {Promise}
 */
export function submitBankCardInfo(data) {
  return request({
    url: '/api/wallet/binding-info',
    method: 'post',
    data
  })
} 