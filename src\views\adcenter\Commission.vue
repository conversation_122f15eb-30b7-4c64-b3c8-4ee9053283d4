<template>
  <nav-bar :title="t('commission.center')" />

  <div class="content">
    <!-- 佣金概览卡片 -->
    <div class="commission-overview">
      <div class="commission-item">
        <div class="commission-label">{{ t('commission.total') }}</div>
        <div class="commission-value">$0.00</div>
      </div>

      <div class="commission-item">
        <div class="commission-label">{{ t('commission.today') }}</div>
        <div class="commission-value">$0.00</div>
      </div>

      <div class="commission-item">
        <div class="commission-label">{{ t('commission.thisWeek') }}</div>
        <div class="commission-value">$0.00</div>
      </div>

      <div class="commission-item">
        <div class="commission-label">{{ t('commission.thisMonth') }}</div>
        <div class="commission-value">$0.00</div>
      </div>
    </div>

    <!-- 团队统计信息 -->
    <div class="team-stats">
      <div class="stats-item">
        <div class="stats-label">{{ t('commission.teamMembers') }}</div>
        <div class="stats-value">0</div>
      </div>

      <div class="stats-item">
        <div class="stats-label">{{ t('commission.directMembers') }}</div>
        <div class="stats-value">0</div>
      </div>

      <div class="stats-item">
        <div class="stats-label">{{ t('commission.indirectMembers') }}</div>
        <div class="stats-value">0</div>
      </div>
    </div>

    <!-- Tab 选项卡 -->
    <VanTabs v-model:active="activeTab" class="custom-tabs">
      <VanTab :title="t('commission.teamMembers')">
        <!-- 团队成员筛选 DropdownMenu -->
        <VanDropdownMenu>
          <VanDropdownItem v-model="teamFilter" :options="teamFilterOptions" />
        </VanDropdownMenu>

        <!-- 团队成员列表 -->
        <VanEmpty 
          class="custom-empty" 
          :description="t('commission.noMoreData')" 
          image="search" 
        />
      </VanTab>

      <VanTab :title="t('commission.details')">
        <!-- 佣金明细筛选 DropdownMenu -->
        <VanDropdownMenu>
          <VanDropdownItem v-model="detailsFilter" :options="detailsFilterOptions" />
          <VanDropdownItem v-model="dateRangeVisible" :title="dateRangeTitle">
            <VanCell :title="t('commission.selectDateRange')" is-link @click="showCalendar = true" />
          </VanDropdownItem>
        </VanDropdownMenu>

        <!-- 佣金明细列表 -->
        <VanEmpty 
          class="custom-empty" 
          :description="t('commission.noMoreData')" 
          image="search" 
        />
      </VanTab>
    </VanTabs>
  </div>

  <!-- 日期选择日历 -->
  <VanCalendar 
    v-model:show="showCalendar" 
    type="range" 
    :min-date="minDate" 
    :max-date="maxDate"
    @confirm="onDateRangeConfirm" 
  />
</template>

<script setup>
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useTitle } from '@/utils/useTitle'
import NavBar from '@/components/NavBar.vue'
import { 
  Tabs as VanTabs, 
  Tab as VanTab, 
  DropdownMenu as VanDropdownMenu, 
  DropdownItem as VanDropdownItem, 
  Cell as VanCell, 
  Calendar as VanCalendar,
  Empty as VanEmpty
} from 'vant'

const { t } = useI18n()
useTitle(t('commission.center'))

// 选项卡状态
const activeTab = ref(0)

// 团队成员筛选配置
const teamFilter = ref('all')
const teamFilterOptions = computed(() => [
  { text: t('commission.all'), value: 'all' },
  { text: t('commission.directMembers'), value: 'direct' },
  { text: t('commission.indirectMembers'), value: 'indirect' }
])

// 佣金明细筛选配置
const detailsFilter = ref('all')
const detailsFilterOptions = computed(() => [
  { text: t('commission.all'), value: 'all' },
  { text: t('commission.directCommission'), value: 'direct' },
  { text: t('commission.indirectCommission'), value: 'indirect' }
])

// 日期范围选择
const showCalendar = ref(false)
const dateRangeVisible = ref('')
const selectedDateRange = ref([])
const minDate = new Date(new Date().getFullYear() - 1, 0, 1)
const maxDate = new Date()

// 日期范围标题
const dateRangeTitle = computed(() => {
  if (selectedDateRange.value && selectedDateRange.value.length === 2) {
    const startDate = formatDate(selectedDateRange.value[0])
    const endDate = formatDate(selectedDateRange.value[1])
    return `${startDate} - ${endDate}`
  }
  return t('commission.dateRange')
})

// 格式化日期
function formatDate(date) {
  if (!date) return ''
  const d = new Date(date)
  return `${d.getMonth() + 1}/${d.getDate()}`
}

// 确认日期范围
function onDateRangeConfirm(dates) {
  selectedDateRange.value = dates
  showCalendar.value = false
}
</script>

<style scoped>
.content {
  padding: 0.16rem;
}

.commission-overview {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.16rem;
  padding: 0.16rem;
  background-color: #1877F2; /* Facebook 主色调 */
  color: white;
  border-radius: 0.1rem;
  margin-bottom: 0.16rem;
}

.commission-item {
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 0.1rem;
  padding: 0.2rem 0.16rem;
  display: flex;
  flex-direction: column;
  transition: background-color 0.3s;
}

.commission-item:hover {
  background-color: rgba(255, 255, 255, 0.25);
}

.commission-label {
  font-size: 0.14rem;
  margin-bottom: 0.05rem;
  opacity: 0.9;
}

.commission-value {
  font-size: 0.24rem;
  font-weight: bold;
}

.team-stats {
  display: flex;
  justify-content: space-between;
  background-color: white;
  padding: 0.16rem;
  border-radius: 0.1rem;
  margin-bottom: 0.16rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.stats-item {
  flex: 1;
  text-align: center;
}

.stats-label {
  font-size: 0.14rem;
  color: #65676B; /* Facebook 次要文本颜色 */
  margin-bottom: 0.05rem;
}

.stats-value {
  font-size: 0.24rem;
  font-weight: bold;
  color: #1C1E21; /* Facebook 主要文本颜色 */
}

.custom-empty {
  margin-top: 0.3rem;
  padding-bottom: 0.3rem;
}

/* 自定义样式使Empty组件符合Facebook风格 */
:deep(.custom-empty .van-empty__image) {
  width: 0.9rem;
  height: 0.9rem;
}

:deep(.custom-empty .van-empty__description) {
  color: #8A8D91;
  font-size: 0.14rem;
  line-height: 0.2rem;
  margin-top: 0.12rem;
}

/* 自定义Tabs样式 */
:deep(.custom-tabs .van-tabs__line) {
  background-color: #1877F2;
}

:deep(.custom-tabs .van-tab--active) {
  color: #1877F2;
  font-weight: 500;
}

:deep(.custom-tabs .van-tab) {
  color: #65676B;
}
</style>