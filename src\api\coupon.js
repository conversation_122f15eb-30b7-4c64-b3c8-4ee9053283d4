/**
 * 优惠券相关的API请求
 */
import request from '@/utils/request'

/**
 * 获取优惠券列表
 * @param {Object} params 查询参数
 * @param {Number} params.page 页码
 * @param {Number} params.pageSize 每页数量
 * @param {String} params.status 优惠券状态 (all/unused/used)
 * @returns {Promise}
 */
export function fetchCouponList(params) {
  return request({
    url: '/api/promotion/coupons',
    method: 'get',
    params
  })
}

/**
 * 获取可用优惠券列表
 * @param {Object} params 查询参数
 * @param {Number} params.amount 订单金额
 * @param {String} params.planId 计划ID
 * @returns {Promise}
 */
export function fetchAvailableCoupons(params) {
  return request({
    url: '/api/promotion/coupons',
    method: 'get',
    params: {
      ...params,
      status: 'unused' // 只获取未使用的优惠券
    }
  })
}

/**
 * 使用优惠券
 * @param {Object} data 请求数据
 * @param {String} data.couponId 优惠券ID
 * @param {String} data.planId 计划ID
 * @param {Number} data.amount 订单金额
 * @returns {Promise}
 */
export function useCoupon(data) {
  return request({
    url: '/api/promotion/coupons/use',
    method: 'post',
    data
  })
}

/**
 * 领取新用户优惠
 * @returns {Promise}
 */
export function claimNewUserDiscount() {
  return request({
    url: '/api/promotion/coupons/claim-new-user',
    method: 'post'
  })
} 