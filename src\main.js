import { createApp } from 'vue'
import App from './App.vue'
import router from '@/router'
import pinia from '@/stores'
import i18n from '@/i18n'
import {
  Button,
  NavBar,
  Icon,
  Tabbar,
  TabbarItem,
  Cell,
  CellGroup,
  Form,
  Field,
  Toast,
  Dialog,
  Notify,
  Popup,
  Loading,
  ActionSheet,
  Empty,
  ConfigProvider,
  Locale
} from 'vant'
import enUS from 'vant/es/locale/lang/en-US'
import zhCN from 'vant/es/locale/lang/zh-CN'
import 'vant/lib/index.css'
import './style.css'
import { watch } from 'vue'

// 设置应用标题
document.title = import.meta.env.VITE_APP_TITLE || 'Facebook Ads'

const app = createApp(App)

// 根据当前选择的语言设置Vant的语言
const setVantLocale = () => {
  const currentLocale = i18n.global.locale.value
  if (currentLocale === 'zh') {
    Locale.use('zh-C<PERSON>', zhCN)
  } else {
    Locale.use('en-US', enUS)
  }
}

// 初始设置Vant的语言
setVantLocale()

// 使用Vue的watch函数监听语言变化
watch(() => i18n.global.locale.value, (newLocale) => {
  setVantLocale()
})

// 注册Vant组件
app.use(Button)
app.use(NavBar)
app.use(Icon)
app.use(Tabbar)
app.use(TabbarItem)
app.use(Cell)
app.use(CellGroup)
app.use(Form)
app.use(Field)
app.use(Toast)
app.use(Dialog)
app.use(Notify)
app.use(Popup)
app.use(Loading)
app.use(ActionSheet)
app.use(Empty)
app.use(ConfigProvider)

app.use(router)
app.use(pinia)
app.use(i18n)

app.mount('#app')
