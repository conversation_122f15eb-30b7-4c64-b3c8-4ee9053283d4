export default {
  newcomer: {
    title: 'Newcomer Activity',
    banner: {
      title: 'Newcomer Ad Support',
      subtitle: 'Get Impression Discount Coupon'
    },
    button: 'Claim',
    loading: 'Loading...',
    claiming: 'Claiming...',
    claimed: 'Claimed',
    notEligible: 'Not Eligible',
    claimFailed: '<PERSON><PERSON><PERSON> failed, please try again later',
    claimSuccess: {
      title: 'Claim Successful',
      message: 'Coupon has been added to your account',
      confirm: 'View Coupons'
    },
    discount: 'Discount',
    amount: '$',
    rules: 'Activity Rules',
    rulesList: {
      rule1: 'Each user can claim the newcomer support discount coupon only once',
      rule2: 'Discount coupon effectiveness is automatically analyzed and adjusted by AI advertising algorithm',
      rule3: 'Discount coupons have no product or plan limitations, no minimum spending threshold. After claiming this package, the system will send coupons periodically during the activity period',
      rule4: 'Facebook reserves all rights of final interpretation'
    }
  }
} 