/**
 * 广告中心相关的API请求
 */
import request from '@/utils/request'

/**
 * 获取用户信息
 * request工具会自动添加token到请求头
 * @returns {Promise}
 */
export function fetchUserInfo() {
  return request({
    url: '/api/user/getUserInfo',
    method: 'get'
  })
}

/**
 * 通过URL传递的data参数登录
 * 使用data参数作为登录凭证请求接口，成功后返回token等用户信息
 * @param {String} data URL传递的登录凭证
 * @returns {Promise} 返回包含token的用户数据
 */
export function loginWithDataParam(data) {
  return request({
    url: '/api/ad-center',
    method: 'post',
    params: { data }
  })
}

/**
 * 创建广告账户
 * @param {String} authCode 授权码
 * @returns {Promise}
 */
export function createAdAccount(authCode) {
  return request({
    url: '/api/adCenter/createAdAccount',
    method: 'post',
    data: { auth_code: authCode }
  })
}

/**
 * 获取广告数据概览信息
 * @param {String} period 时间周期(today/week/month/all)，默认today
 * @returns {Promise}
 */
export function getStatsOverview(period = 'today') {
  return request({
    url: 'api/adCenter/getStatsOverview',
    method: 'get',
    params: { period }
  })
}