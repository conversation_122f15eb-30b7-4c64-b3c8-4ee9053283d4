export default {
  notification: {
    title: 'Notifications',
    filters: {
      all: 'All Messages',
      system: 'System Announcements',
      activity: 'Activity Notifications',
      maintenance: 'System Maintenance',
      allStatus: 'All Status',
      unread: 'Unread',
      read: 'Read'
    },
    categories: {
      activity: 'Activity',
      system: 'System',
      maintenance: 'Maintenance'
    },
    status: {
      pending: 'Pending',
      processing: 'Processing',
      completed: 'Completed'
    },
    confirm: 'Confirm',
    timeAgo: {
      justNow: 'Just now',
      minutesAgo: '{n} minutes ago',
      hoursAgo: '{n} hours ago',
      daysAgo: '{n} days ago'
    },
    markAllRead: 'Mark All as Read',
    markAllReadSuccess: 'All messages marked as read',
    markAllReadFailed: 'Failed to mark all as read, please try again later',
    viewDetails: 'View Details',
    emptyList: 'No notifications'
  }
} 