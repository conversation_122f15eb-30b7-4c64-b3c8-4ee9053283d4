import request from '@/utils/request'

/**
 * 提交计划跟随申请
 * @param {String} agencyId - 代运营ID
 * @returns {Promise}
 */
export function followAgency(agencyId) {
  return request({
    url: 'api/agency/followAgency',
    method: 'post',
    data: { agencyId }
  })
}

/**
 * 获取跟随记录
 * @param {Number} page - 页码，默认1
 * @param {Number} pageSize - 每页条数，默认10
 * @returns {Promise}
 */
export function getFollowRecords(params) {
  return request({
    url: 'api/agency/getFollowRecords',
    method: 'get',
    params
  })
} 