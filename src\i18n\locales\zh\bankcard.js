export default {
    bankcard: {
        // 银行卡绑定
        bankCardTitle: '绑定银行卡',
        bankName: '银行名称',
        beneficiaryName: '持卡人姓名',
        accountNumber: '银行卡账号',
        routingNumber: '汇款路线号码',
        beneficiaryAddress: '收款人地址',
        bankAddress: '银行地址',
        inputBankName: '请输入银行名称',
        inputBeneficiaryName: '请输入持卡人姓名',
        inputAccountNumber: '请输入银行卡账号',
        inputRoutingNumber: '请输入汇款路线号码',
        inputBeneficiaryAddress: '请输入收款人地址',
        inputBankAddress: '请输入银行地址',
        submit: '提交',

        // 表单验证
        fieldRequired: '{field}不能为空',
        submitSuccess: '提交成功',
    }
}
